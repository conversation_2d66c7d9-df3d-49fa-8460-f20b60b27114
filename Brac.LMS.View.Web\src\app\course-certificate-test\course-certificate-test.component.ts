import {
  Component,
  TemplateRef,
  OnInit,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { Location } from '@angular/common';
import { FormBuilder } from '@angular/forms';
import { environment } from '../../environments/environment';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonService } from '../_services/common.service';
import { BlockUI, NgBlockUI } from 'ng-block-ui';
import { Timer } from 'src/app/_models/timer';
import { ConfirmService } from 'src/app/_helpers/confirm-dialog/confirm.service';
import { NgxSmartModalService, NgxSmartModalComponent } from 'ngx-smart-modal';
import { ToastrService } from 'ngx-toastr';
import { ResponseStatus } from 'src/app/_models/enum';
import { Subscription, findIndex } from 'rxjs';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { LocalStorageHelper } from 'src/app/_helpers/local-storage-helper';
import { AuthenticationService } from '../_services/authentication.service';
import { trigger, transition, style, animate } from '@angular/animations';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Inject, HostListener } from '@angular/core';
import { Page } from '../_models/page';
import { DOCUMENT } from '@angular/common';
import { PaginationService } from '../shared/pagination/pagination.service';
@Component({
  selector: 'app-course-certificate-test',
  templateUrl: './course-certificate-test.component.html',
  styleUrls: ['./course-certificate-test.component.css'],
  animations: [
    trigger('inOutAnimation', [
      transition(':enter', [
        style({ height: 0, opacity: 0 }),
        animate('0.5s ease-out', style({ height: 50, opacity: 1 })),
      ]),
      transition(':leave', [
        style({ height: 50, opacity: 1 }),
        animate('0.5s ease-in', style({ height: 0, opacity: 0 })),
      ]),
    ]),
  ],
})
export class CourseCertificateTestComponent implements OnInit {
  private window: Window;
  baseUrl = environment.baseUrl;
  mediaBaseUrl = environment.mediaBaseUrl;
  @ViewChild('fullScreenElement') fullScreenElement!: ElementRef;
  private currentUser: any;
  private quiz: any;
  private worker: Worker;
  page = new Page();
  examId: string;
  traineeEexamId: string;
  pagedetail: any;
  quizRunning: boolean = false;
  questionList: Array<any> = [];
  qIndex: number = -1;
  btnNextText: string = 'Next';
  noOfAnsweredQs: number = 0;
  mcqList: Array<any> = [];
  timer: Timer = new Timer();
  result: any;
  @ViewChild(TemplateRef, { static: false }) tpl: TemplateRef<any>;
  @ViewChild('template') template: TemplateRef<any>;
  @BlockUI() blockUI: NgBlockUI;
  currentTime: Date;
  examTime: Date;
  allowAnswerSubmit: boolean = false;
  changeScreenCounter: number = 0;
  timerSubscription: Subscription;
  examTimeCheckerInterval: any;
  notAllowMessage: string;
  terminated = null;
  interval: any;
  allowTabChange = false;
  isConnected = true;
  answerStatus = '';
  examStarted = false;
  modalRef: BsModalRef;
  modalConfig: any = { class: 'gray', backdrop: 'static' };
  definitions: any[];
  mqAnswered: boolean = false;
  popStateHandler: any;
  answerSubmitted: boolean = false;

  constructor(
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
    private _service: CommonService,
    public ngxSmartModalService: NgxSmartModalService,
    public modalService: BsModalService,
    private toastr: ToastrService,
    private confirmService: ConfirmService,
    private authService: AuthenticationService,
    private route: ActivatedRoute,
    private pageService: PaginationService,
    private _location: Location
  ) {
    this.examId = this.route.snapshot.paramMap.get('examId');
    this.baseUrl = environment.baseUrl;
    this.window = this.document.defaultView;
    this.authService.getCurrentUser().subscribe((user) => {
      this.currentUser = user;
    });

    this.page.startsFrom = 1;
    this.page.pageNumber = 1;
    this.page.size = 1;
  }

  ngOnInit(): void {
    this.answerSubmitted = false;
    this.getCourseCertificateTestDetails();
  }

  ngAfterViewInit() {
    this.popStateHandler = (event) => {
      if (this.examStarted) {
        alert('Your exam has been terminated!');
        this.saveAnswer(this.template);
      }
    };
    window.addEventListener('popstate', this.popStateHandler);
  }

  ngOnDestroy() {
    if (this.popStateHandler) {
      window.removeEventListener('popstate', this.popStateHandler);
    }
    if (this.timerSubscription) this.timerSubscription.unsubscribe();
    if (this.interval) clearInterval(this.interval);
  }

  getHourMint(duration: number) {
    return Math.floor(duration / 60) + 'h ' + (duration % 60) + 'm';
  }
  selectSpecific(lists) {
    lists.map((x) => (x.Selected = false));
  }
  getCourseCertificateTestDetails() {
    this.blockUI.start('Getting data...');
    this._service
      .get('exam/certificate-test/get-exam-info/' + this.examId)
      .subscribe({
        next: (res: any) => {
          console.trace(res);
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
            this._location.back();
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, 'Error!', {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            this._location.back();
            return;
          }
          this.pagedetail = res.Data;
          this.notAllowMessage = res.Message;
          console.log('this.pagedetail ', this.pagedetail);
          //   this.quizType = res.Data.ExamType === 'CertificateTest' ? 'Comprehension Test' : 'Certification Test';
          if (!res.Data.CanAttend) return;
        },
        error: (err) => {
          this.toastr.warning(err.Messaage || err, 'Error!', {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          this.blockUI.stop();
        },
        complete: () => this.blockUI.stop(),
      });
  }

  startQuiz(template: TemplateRef<any>) {
    this.questionList = [];
    this.examStarted = true;
    const savedExam = LocalStorageHelper.get(
      this.examId + '~' + this.currentUser.Id
    );
    if (savedExam) {
      this.questionList = savedExam.QuestionList;
      this.quiz = {
        StartDate: savedExam.StartDate
          ? savedExam.StartDate
          : new Date().toISOString(),
      };

      this.qIndex = savedExam.QIndex;
      this.quizRunning = true;
      this.allowAnswerSubmit = true;

      this.timerSubscription = this.timer
        .start(savedExam.ExamTime)
        .subscribe((status) => {
          if (status === 'ended') {
            this.onTimesUp(template);
            this.timerSubscription.unsubscribe();
          }
        });
    } else {
      this.blockUI.start('Starting exam. Please wait...');
      this._service
        .get('exam/certificate-test/get-questions/' + this.examId)
        .subscribe({
          next: (res: any) => {
            if (res.Status === ResponseStatus.Warning) {
              this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
              return;
            } else if (res.Status === ResponseStatus.Error) {
              this.toastr.error(res.Message, 'Error!', {
                closeButton: true,
                disableTimeOut: false,
                enableHtml: true,
              });
              return;
            }

            // **FIX: Use ISO string for proper datetime serialization**
            this.quiz = { StartDate: new Date().toISOString() };
            this.traineeEexamId = res.Data.TraineeExamId;
            res.Data.MCQs.forEach((element) => {
              this.questionList.push({
                Id: element.Id,
                Question: element.Question,
                Options: [
                  { Text: element.Option1, Selected: false },
                  { Text: element.Option2, Selected: false },
                  { Text: element.Option3, Selected: false },
                  { Text: element.Option4, Selected: false },
                ],
                Mark: element.Mark,
                Type: 'MCQ',
              });
            });

            res.Data.TrueFalsQs.forEach((x) => {
              this.questionList.push({
                Id: x.Id,
                Question: x.Question,
                Mark: x.Mark,
                Answer: false,
                CorrectAnswer: null,
                Type: 'TFQ',
              });
            });

            res.Data.FIGQs.forEach((x) => {
              this.questionList.push({
                Id: x.Id,
                Question: x.Question,
                Mark: x.Mark,
                Answer: null,
                Type: 'FIGQ',
              });
            });

            if (res.Data.MatchingQs) {
              this.questionList.push({
                LeftSides: res.Data.MatchingQs.LeftSides,
                RightSides: res.Data.MatchingQs.RightSides,
                Type: 'LRMQ',
              });
            }

            res.Data.WrittenQs.forEach((x) => {
              this.questionList.push({
                Id: x.Id,
                Question: x.Question,
                Mark: x.Mark,
                Answer: null,
                Type: 'WQ',
              });
            });
            console.log(this.questionList);
            this.qIndex = 0;
            this.quizRunning = true;
            this.allowAnswerSubmit = true;
            if (this.allowAnswerSubmit) {
              this.examTimeChangeScreen(template);
            }
            this.timerSubscription = this.timer
              .start(this.pagedetail.DurationMnt * 60)
              .subscribe((status) => {
                if (status === 'ended') {
                  this.onTimesUp(template);
                  this.timerSubscription.unsubscribe();
                }
              });
          },
          error: (err) => {
            this.toastr.error(err.message || err, 'Error!', {
              closeButton: true,
              disableTimeOut: true,
              enableHtml: true,
            });
            this.blockUI.stop();
          },
          complete: () => this.blockUI.stop(),
        });

      // window.addEventListener('beforeunload', function (event) {
      //   event.preventDefault();

      // });
      // window.addEventListener('unload', (event) => {
      //   this.submitAnswer(this.template);
      // });
    }
  }
  // @HostListener('window:beforeunload', ['$event'])
  // onBeforeUnload(event: BeforeUnloadEvent): boolean {
  //   alert();
  //   event.returnValue = "Your answer will be submitted"
  //   event.preventDefault();
  //  return false;
  //   // Show a confirmation message to the user
  //   // const confirmationMessage = 'Are you sure you want to leave?';
  //   // (event as any).returnValue = confirmationMessage; // Standard for most browsers
  //   // return confirmationMessage; // For some older browsers
  //   //this.saveAnswer(this.template)
  // }
  private prepareDataToSubmit() {
    this.examStarted = false;
    let submittedMCQList = [],
      submittedTFQList = [],
      submittedFIGQList = [],
      submittedLRMQList = [],
      submittedWQList = [];
    this.questionList
      .filter((x) => x.Type === 'MCQ')
      .forEach((element) => {
        submittedMCQList.push({
          QuestionId: element.Id,
          Answered: element.Options.map(function (x, i) {
            if (x.Selected) return i + 1;
            else return 0;
          })
            .filter((x) => x > 0)
            .join(),
        });
      });
    this.questionList
      .filter((x) => x.Type === 'TFQ')
      .forEach((element) => {
        submittedTFQList.push({
          QuestionId: element.Id,
          Answered: element.Answer,
          CorrectAnswer: !element.Answer
            ? element.CorrectAnswer
              ? element.CorrectAnswer.trim()
              : element.CorrectAnswer
            : null,
        });
      });
    this.questionList
      .filter((x) => x.Type === 'FIGQ')
      .forEach((element) => {
        submittedFIGQList.push({
          QuestionId: element.Id,
          Answered: element.Answer ? element.Answer.trim() : element.Answer,
        });
      });

    let mQuestions = this.questionList.find((x) => x.Type === 'LRMQ');
    if (mQuestions)
      for (let i = 0; i < mQuestions.LeftSides.length; i++) {
        const element = mQuestions.LeftSides[i];
        submittedLRMQList.push({
          QuestionId: element.Id,
          Answered: mQuestions.RightSides[i],
        });
      }

    this.questionList
      .filter((x) => x.Type === 'WQ')
      .forEach((element) => {
        submittedWQList.push({
          QuestionId: element.Id,
          Answered: element.Answer ? element.Answer.trim() : element.Answer,
        });
      });

    const data = {
      ExamId: this.examId,
      // **FIX: Use ISO strings for proper datetime serialization**
      StartTime: this.quiz.StartDate,
      EndTime: new Date().toISOString(),
      AutoSubmission: true,
      MCQList: submittedMCQList,
      TFQList: submittedTFQList,
      FIGQList: submittedFIGQList,
      MatchingQList: submittedLRMQList,
      WQList: submittedWQList,
      Terminated: this.terminated,
    };
    return data;
  }
  // @HostListener('window:unload', ['$event'])
  // unloadHandler(event: any): void {
  //   let url=this.baseUrl+'exam/certificate-test/save-answers';

  //   navigator.sendBeacon(url, this.prepareDataToSubmit());
  // }

  onPageChange(page: number) {
    this.setPage(page);
  }
  setPage(pageNumber) {
    this.page.pageNumber = pageNumber;
    this.qIndex = pageNumber - 1;
    this.btnNextText =
      this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';
  }
  drop(event: CdkDragDrop<string[]>) {
    this.mqAnswered = true;
    moveItemInArray(
      this.questionList[this.qIndex].RightSides,
      event.previousIndex,
      event.currentIndex
    );
  }

  onTimesUp(template: TemplateRef<any>) {
    if (this.answerSubmitted) return;
    //  this.saveAnswerSheetIntoLocalStorage(true);
    this.ngxSmartModalService.closeAll();
    this.confirmService.close();
    // this.saveAnswer(template, true);

    this.confirmService
      .confirm(
        "Time's Up!!",
        'Your answer will get submitted automatically. ' + this.answerStatus,
        'OK',
        null,
        true
      )
      .subscribe(() => {
        this.answerSubmitted = true;
        this.saveAnswer(template, true);
      });
  }

  onScreenChange(template: TemplateRef<any>) {
    //  this.saveAnswerSheetIntoLocalStorage(true);
    if (this.answerSubmitted) {
      console.log('Answer already submitted, skipping screen change handler');
      return;
    }

    // **FIX: Additional safety checks for terminated submissions**
    if (!this.allowAnswerSubmit) {
      console.log(
        'Answer submission not allowed, forcing submission for terminated exam'
      );
      // For terminated exams, we still need to submit even if allowAnswerSubmit is false
      this.answerSubmitted = true;
      this.saveAnswer(template, true);
      return;
    }

    this.ngxSmartModalService.closeAll();
    this.confirmService.close();

    this.confirmService
      .confirm(
        'Your exam has been terminated!',
        'Your answer will get submitted automatically. ' + this.answerStatus,
        'OK',
        null,
        true
      )
      .subscribe(() => {
        this.answerSubmitted = true;
        this.saveAnswer(template, true);
      });
  }

  nextQuestion(template: TemplateRef<any>) {
    if (this.qIndex < this.questionList.length - 1) this.qIndex++;
    else {
      this.timer.pause();
      this.submitAnswer(template);
      return;
    }
    this.btnNextText =
      this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';
    this.page.pageNumber = this.qIndex + 1;
    this.pageService.currentPage.next(this.page.pageNumber);
  }

  prevQuestion() {
    if (this.qIndex !== 0) this.qIndex--;
    this.btnNextText =
      this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';
    this.page.pageNumber = this.page.pageNumber - 1;
    this.pageService.currentPage.next(this.page.pageNumber);
  }

  submitAnswer(template: TemplateRef<any>) {
    if (this.answerSubmitted) return;
    this.confirmService
      .confirm(
        'Are you sure?',
        'You are going to submit answer. ' + this.answerStatus,
        'Yes, Submit Answer'
      )
      .subscribe((result) => {
        if (result) {
          this.timer.pause();
          this.answerSubmitted = true;
          this.saveAnswer(template);
        } else {
          this.timer.resume();
        }
      });
  }

  private saveAnswer(
    template: TemplateRef<any>,
    autoSubmission: boolean = false
  ) {
    if (!this.isConnected) {
      this.timer.resume();
      return;
    }
    this.examStarted = false;
    let submittedMCQList = [],
      submittedTFQList = [],
      submittedFIGQList = [],
      submittedLRMQList = [],
      submittedWQList = [];
    this.questionList
      .filter((x) => x.Type === 'MCQ')
      .forEach((element) => {
        submittedMCQList.push({
          QuestionId: element.Id,
          Answered: element.Options.map(function (x, i) {
            if (x.Selected) return i + 1;
            else return 0;
          })
            .filter((x) => x > 0)
            .join(),
        });
      });
    this.questionList
      .filter((x) => x.Type === 'TFQ')
      .forEach((element) => {
        submittedTFQList.push({
          QuestionId: element.Id,
          Answered: element.Answer,
          CorrectAnswer: !element.Answer
            ? element.CorrectAnswer
              ? element.CorrectAnswer.trim()
              : element.CorrectAnswer
            : null,
        });
      });
    this.questionList
      .filter((x) => x.Type === 'FIGQ')
      .forEach((element) => {
        submittedFIGQList.push({
          QuestionId: element.Id,
          Answered: element.Answer ? element.Answer.trim() : element.Answer,
        });
      });

    let mQuestions = this.questionList.find((x) => x.Type === 'LRMQ');
    if (mQuestions)
      for (let i = 0; i < mQuestions.LeftSides.length; i++) {
        const element = mQuestions.LeftSides[i];
        submittedLRMQList.push({
          QuestionId: element.Id,
          Answered: mQuestions.RightSides[i],
        });
      }

    this.questionList
      .filter((x) => x.Type === 'WQ')
      .forEach((element) => {
        submittedWQList.push({
          QuestionId: element.Id,
          Answered: element.Answer ? element.Answer.trim() : element.Answer,
        });
      });

    const obj = {
      ExamId: this.examId,
      TraineeExamId: this.traineeEexamId,
      // **FIX: Use ISO strings for proper datetime serialization**
      StartTime: this.quiz.StartDate,
      EndTime: new Date().toISOString(),
      AutoSubmission: autoSubmission,
      MCQList: submittedMCQList,
      TFQList: submittedTFQList,
      FIGQList: submittedFIGQList,
      MatchingQList: submittedLRMQList,
      WQList: submittedWQList,
      Terminated: this.terminated,
    };
    // **FIX: Log terminated submissions for debugging**
    if (this.terminated) {
      console.log('TERMINATED SUBMISSION:', {
        terminated: this.terminated,
        autoSubmission: autoSubmission,
        mcqCount: obj.MCQList?.length || 0,
        tfqCount: obj.TFQList?.length || 0,
        figCount: obj.FIGQList?.length || 0,
        matchingCount: obj.MatchingQList?.length || 0,
        writtenCount: obj.WQList?.length || 0,
      });
    }

    this.blockUI.start('Submitting answer. Please wait...');
    this._service.post('exam/certificate-test/save-answers', obj).subscribe({
      next: (res: any) => {
        this.timerSubscription.unsubscribe();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.error(res.Message, 'Warning!', {
            closeButton: true,
            disableTimeOut: true,
            enableHtml: true,
          });
          console.log(res.Data);
          this.timer.resume();
          // return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, 'Error!', {
            closeButton: true,
            disableTimeOut: true,
            enableHtml: true,
          });
          //this.timer.resume();
          console.log(res.Data);
          this.timerSubscription.unsubscribe();
          // this._location.back();
          // return;
        }
        // this._location.back();

        this.confirmService.close();
        this.toastr.success(res.Message, 'Answer Submission!', {
          timeOut: 4000,
        });
        this.allowAnswerSubmit = false;

        if (!res.Data) this._location.back();
        else {
          this.result = res.Data;
          this.modalRef = this.modalService.show(template, this.modalConfig);
        }
      },
      error: (err) => {
        // this._location.back();
        console.log(err);
        // this.timer.resume();
        this.timerSubscription.unsubscribe();
        this.toastr.error(err.message || err, 'Error!', {
          closeButton: true,
          disableTimeOut: true,
          enableHtml: true,
        });
        this.blockUI.stop();
      },
      complete: () => {
        this.blockUI.stop();
      },
    });
  }
  downloadCertificate(item) {
    this.blockUI.start('Generating certificate. Please wait...');
    this._service
      .downloadFile('course/download-certificate/' + item)
      .subscribe({
        next: (res) => {
          // this.pdfViewerOnDemand.pdfSrc = res; // pdfSrc can be Blob or Uint8Array
          // this.pdfViewerOnDemand.refresh();
          // this.ngxSmartModalService.create('certificateModal', this.tpl).open();

          const url = window.URL.createObjectURL(res);
          var link = document.createElement('a');
          link.href = url;
          link.target = 'blank';
          link.rel = 'noopener';
          link.download = item.Title + ' Certificate.pdf';
          link.click();
        },
        error: (err) => {
          this.toastr.error(err.message || err, 'Error!', { timeOut: 2000 });
          this.blockUI.stop();
        },
        complete: () => this.blockUI.stop(),
      });
  }

  backClicked(examStarted) {
    if (!examStarted) {
      this._location.back();
    } else {
      this.confirmService
        .confirm(
          'Warning!',
          'Your answer will get submitted automatically. ' + this.answerStatus,
          'OK',
          'CANCEL',
          false
        )
        .subscribe((res) => {
          if (res) {
            this.saveAnswer(this.template);
            this._location.back();
          }
        });
    }
  }
  examTimeChangeScreen(template: TemplateRef<any>) {
    document.addEventListener('visibilitychange', (event) => {
      if (document.visibilityState != 'visible') {
        if (this.changeScreenCounter === 0 && this.allowAnswerSubmit == true) {
          alert(
            'You are not allowed to change the screen!!! If you try one more step to change the screen the exam will be terminated!!!'
          );
          this.changeScreenCounter++;
        } else if (
          this.changeScreenCounter === 1 &&
          this.allowAnswerSubmit == true
        ) {
          // **FIX: Prevent race conditions in terminated submission**
          // Set terminated flag first
          this.terminated = true;

          // Stop timer and quiz state immediately to prevent conflicts
          if (this.timerSubscription) {
            this.timerSubscription.unsubscribe();
          }
          this.quizRunning = false;
          this.allowAnswerSubmit = false;
          this.changeScreenCounter++;

          // Call screen change handler after state is stabilized
          this.onScreenChange(template);
        }
      }
    });
  }
  tryAgain() {
    this.modalHide();
  }
  modalHide() {
    this.modalRef.hide();
    this._location.back();
  }
}
