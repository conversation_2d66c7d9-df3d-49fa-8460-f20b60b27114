﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.Models;
using FastReport.Utils;
using Microsoft.AspNet.Identity;
using Microsoft.Graph.Models;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin.Security;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using Brac.LMS.DB;
using Microsoft.AspNet.Identity.Owin;
using Azure;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/exam")]
    public class ExamController : ApplicationController, IDisposable
    {
        private readonly IExamService _service;
        private ApplicationUserManager _userManager;
        private bool _disposed = false;

        public ExamController()
        {
            _service = new ExamService();
        }
        public ApplicationUserManager UserManager
        {
            get
            {
                return _userManager ?? Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
            private set
            {
                _userManager = value;
            }
        }
        #region Certification Test
        [Authorize(Roles = "Admin"), HttpPost, Route("certificate-test/create-or-update")]
        public async Task<IHttpActionResult> CertificationTestCreateOrUpdate()
        {
            try
            {
                var model = Newtonsoft.Json.JsonConvert.DeserializeObject<CourseExamModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
                {
                    return Ok(await _nservice.CourseExamCreateOrUpdate(model, User.Identity));
                }
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("certificate-test/mcq/update")]
        public async Task<IHttpActionResult> ModifyMCQForExam(MCQQuestionModel model)
        {
            using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.ModifyMCQForExam(model, User.Identity));
            }
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("certificate-test/true-false/update")]
        public async Task<IHttpActionResult> ModifyTrueFalseForExam(TrueFalseQuestionModel model)
        {
            using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.ModifyTrueFalseForExam(model, User.Identity));
            }
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("certificate-test/fill-in-the-gap/update")]
        public async Task<IHttpActionResult> ModifyFIGForExam(FIGQuestionModel model)
        {
            using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.ModifyFIGForExam(model, User.Identity));
            }
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("certificate-test/matching/update")]
        public async Task<IHttpActionResult> ModifyLRMForExam(MatchingQuestionModel model)
        {
            using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.ModifyLRMForExam(model, User.Identity));
            }
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("certificate-test/written/update")]
        public async Task<IHttpActionResult> ModifyWrittenForExam(WrittenQuestionModel model)
        {
            using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.ModifyWrittenForExam(model, User.Identity));
            }
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get/{id}")]
        public async Task<IHttpActionResult> GetCourseExamById(Guid id)
        {
            return Ok(await _service.GetCourseExamById(id));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/list/{courseId}")]
        public async Task<IHttpActionResult> GetCourseExamList(Guid courseId)
        {
            return Ok(await _service.GetCourseExamList(courseId));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-mcq-list/{examId}")]
        public async Task<IHttpActionResult> GetMCQQuestionList(Guid examId)
        {
            return Ok(await _service.GetMCQQuestionList(examId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-true-false-list/{examId}")]
        public async Task<IHttpActionResult> GetTrueFalseQuestionList(Guid examId)
        {
            return Ok(await _service.GetTrueFalseQuestionList(examId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-fill-in-the-gap-list/{examId}")]
        public async Task<IHttpActionResult> GetFIGQuestionList(Guid examId)
        {
            return Ok(await _service.GetFIGQuestionList(examId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-matching-list/{examId}")]
        public async Task<IHttpActionResult> GetMatchingQuestionList(Guid examId)
        {
            return Ok(await _service.GetMatchingQuestionList(examId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-written-list/{examId}")]
        public async Task<IHttpActionResult> GetWrittenQuestionList(Guid examId)
        {
            return Ok(await _service.GetWrittenQuestionList(examId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-mcq-list-excel/{examId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetMCQQuestionListExcel(Guid examId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetMCQQuestionListExcel(examId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-true-false-list-excel/{examId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetTrueFalseQuestionListExcel(Guid examId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetTrueFalseQuestionListExcel(examId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-fill-in=the=gap-list-excel/{examId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetFIGQuestionListExcel(Guid examId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetFIGQuestionListExcel(examId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-matching-list-excel/{examId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetMatchingQuestionListExcel(Guid examId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetMatchingQuestionListExcel(examId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-written-list-excel/{examId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetWrittenQuestionListExcel(Guid examId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetWrittenQuestionListExcel(examId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/delete-mcq/{id}")]
        public async Task<IHttpActionResult> DeleteMCQById(long id)
        {
            return Ok(await _service.DeleteQuestionById(id, QuesType.MCQ));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/delete-true-false/{id}")]
        public async Task<IHttpActionResult> DeleteTrueFalseQuestionById(long id)
        {
            return Ok(await _service.DeleteQuestionById(id, QuesType.TrueFalse));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/delete-fill-in-the-gap/{id}")]
        public async Task<IHttpActionResult> DeleteFIGQuestionById(long id)
        {
            return Ok(await _service.DeleteQuestionById(id, QuesType.FIG));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/delete-matching/{id}")]
        public async Task<IHttpActionResult> DeleteMatchingQuestionById(long id)
        {
            return Ok(await _service.DeleteQuestionById(id, QuesType.Matching));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/delete-written/{id}")]
        public async Task<IHttpActionResult> DeleteWrittenQuestionById(long id)
        {
            return Ok(await _service.DeleteQuestionById(id, QuesType.Written));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-trainee-exam-list")]
        public async Task<IHttpActionResult> GetTraineeExamList(Guid courseId, ExamStatus? examStatus, Guid? traineeId, int size, int pageNumber)
        {
            return Ok(await _service.GetTraineeExamList(courseId, examStatus, traineeId, size, pageNumber));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/extend-trainee-exam-quota")]
        public async Task<IHttpActionResult> ExtendTraineeExamQuota(Guid examId, Guid? traineeId,int extendedQuota)
        {
            return Ok(await _service.ExtendTraineeExamQuota(examId, traineeId, extendedQuota));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-unpublished-trainee-exam-list/{courseId}")]
        public async Task<IHttpActionResult> GetUnpublishedTraineeExamList(Guid courseId)
        {
            return Ok(await _service.GetUnpublishedTraineeExamList(courseId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-trainee-answer-sheet/{id}")]
        public async Task<IHttpActionResult> GetAnswerSheetForTrainee(Guid id)
        {
            return Ok(await _service.GetAnswerSheetForTrainee(id));
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("certificate-test/save-marking")]
        public async Task<IHttpActionResult> SaveExamMarking(ExamMarkingModel model)
        {
            using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.SaveExamMarking(model, CurrentUser));
            }
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("certificate-test/upload-marking-file")]
        public async Task<IHttpActionResult> UploadTraineeAnswersheet()
        {
            var _nservice = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.UploadTraineeAnswersheet(CurrentUser));
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("certificate-test/publish/{courseId}")]
        public async Task<IHttpActionResult> PublishTraineeExam(Guid courseId, List<Guid> traineeExamIds)
        {
            using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.PublishTraineeExam(courseId, traineeExamIds, CurrentUser));
            }
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/download-trainee-answersheet-excel")]
        public async Task<HttpResponseMessage> GetTraineeAnswersheetExcel(Guid examId, string status)
        {
            byte[] byteArray = await _service.GetTraineeAnswersheetExcel(examId, status);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }


        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/download-list-in-excel")]
        public async Task<HttpResponseMessage> GetTraineeExamExcel(Guid courseId, ExamStatus? examStatus, Guid? traineeId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetTraineeExamExcel(courseId, examStatus, traineeId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("certificate-test/get-answer-sheet-in-pdf/{id}")]
        public async Task<HttpResponseMessage> GetAnswerSheetPDF(Guid id)
        {
            byte[] byteArray = await _service.GetAnswerSheetPDF(id);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
            //    return response;
            return response;

        }
        #endregion

        #region DownloadSampleMCQ

        [Authorize(Roles = "Admin"), HttpGet, Route("download-sample-question")]
        public HttpResponseMessage DownloadSampleMCQ(QuesType quesType)
        {
            string filePath = string.Empty;
            switch (quesType)
            {
                case QuesType.MCQ:
                    filePath = Generator.UploadSampleMCQ;
                    break;
                case QuesType.TrueFalse:
                    filePath = Generator.UploadSampleTFQ;
                    break;
                case QuesType.FIG:
                    filePath = Generator.UploadSampleFIGQ;
                    break;
                case QuesType.Matching:
                    filePath = Generator.UploadSampleLRMQ;
                    break;
                case QuesType.Written:
                    filePath = Generator.UploadSampleWQ;
                    break;
            }
            if (!File.Exists(filePath))
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }
            else
            {
                HttpResponseMessage result = Request.CreateResponse(HttpStatusCode.OK);
                result.Content = new StreamContent(File.OpenRead(filePath));
                result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                return result;
            }
        } 
        #endregion

        #region Mock Test

        [Authorize(Roles = "Admin"), HttpPost, Route("mock-test/create-or-update")]
        public async Task<IHttpActionResult> MockTestCreateOrUpdate()
        {
            try
            {
                using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
                {
                    var model = Newtonsoft.Json.JsonConvert.DeserializeObject<MockTestModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                    return Ok(await _nservice.MockTestCreateOrUpdate(model, User.Identity));
                }
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("mock-test/dropdown-list/{courseId}")]
        public async Task<IHttpActionResult> GetMockTestDropDownList(Guid courseId)
        {
            return Ok(await _service.GetMockTestDropDownList(courseId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("mock-test/get/{id}")]
        public async Task<IHttpActionResult> GetMockTest(Guid id)
        {
            return Ok(await _service.GetMockTest(id));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("mock-test/list")]
        public async Task<IHttpActionResult> GetCourseMockTestList(Guid courseId)
        {
            return Ok(await _service.GetCourseMockTestList(courseId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("mock-test/get-mcq-list/{testId}")]
        public async Task<IHttpActionResult> GetMockTestMCQQuestionList(Guid testId)
        {
            return Ok(await _service.GetMockTestMCQQuestionList(testId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("mock-test/get-mcq-list-excel/{testId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetMockTestMCQQuestionListExcel(Guid testId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetMockTestMCQQuestionListExcel(testId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }


        [Authorize(Roles = "Admin"), HttpGet, Route("mock-test/delete-mcq/{id}")]
        public async Task<IHttpActionResult> DeleteMockTestMCQQuestionById(long id)
        {
            return Ok(await _service.DeleteMockTestMCQQuestionById(id));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("mock-test/get-trainee-exam-list")]
        public async Task<IHttpActionResult> GetTraineeMockTestList(Guid testId, Guid? traineeId, int size, int pageNumber)
        {
            return Ok(await _service.GetTraineeMockTestList(testId, traineeId, size, pageNumber));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("mock-test/download-list-in-excel")]
        public async Task<HttpResponseMessage> GetTraineeMockTestExcel(Guid testId, Guid? traineeId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetTraineeMockTestExcel(testId, traineeId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }
        #endregion


        #region Trainee Panel API
        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("certificate-test/get-exam-info/{id}")]
        public async Task<IHttpActionResult> GetCourseExamInfoById(Guid id)
        {
            return Ok(await _service.GetCourseExamInfoById(id, CurrentUser));
        }


        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("certificate-test/get-questions/{id}")]
        public async Task<IHttpActionResult> GetExamQuestions(Guid id)
        {
            return Ok(await _service.GetExamQuestions(id, CurrentUser));
        }


        [Authorize(Roles = "Trainee, Guest"), HttpPost, Route("certificate-test/save-answers")]
        public async Task<IHttpActionResult> SaveCourseExamAnswer(ExamAnserSaveModel model)
        {
            using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.SaveCourseExamAnswer(model, CurrentUser));
            }
        }


        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("certificate-test/get-exam-results")]
        public async Task<IHttpActionResult> GetCourseExamResults(Guid? courseId, int size, int pageNumber)
        {
            return Ok(await _service.GetCourseExamResults(courseId, size, pageNumber, CurrentUser));
        }


        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-my-certificates")]
        public async Task<IHttpActionResult> GetAllCertificates(Guid? courseId, int size, int pageNumber)
        {
            return Ok(await _service.GetAllCertificates(courseId, size, pageNumber, CurrentUser));
        }




        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("mock-test/get-exam-info/{id}")]
        public async Task<IHttpActionResult> GetMockTestInfoById(Guid id)
        {
            return Ok(await _service.GetMockTestInfoById(id, CurrentUser));
        }


        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("mock-test/get-questions/{id}")]
        public async Task<IHttpActionResult> GetMockTestQuestions(Guid id)
        {
            return Ok(await _service.GetMockTestQuestions(id, CurrentUser));
        }


        [Authorize(Roles = "Trainee, Guest"), HttpPost, Route("mock-test/save-answers")]
        public async Task<IHttpActionResult> SaveMockTestAnswer(MockTestAnserSaveModel model)
        {
            using (var _nservice = new ExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.SaveMockTestAnswer(model, CurrentUser));
            }
        }


        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("mock-test/get-exam-results/{examId}")]
        public async Task<IHttpActionResult> GetMockTestResults(Guid examId)
        {
            return Ok(await _service.GetMockTestResults(examId, CurrentUser));
        }

        #endregion

        #region IDisposable Implementation
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _service?.Dispose();
                }

                // Dispose unmanaged resources (if any)

                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        ~ExamController()
        {
            Dispose(false);
        }
        #endregion
    }
}
