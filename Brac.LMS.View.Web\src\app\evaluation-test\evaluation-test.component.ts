import { Component, TemplateRef, OnInit, ViewChild } from '@angular/core';
import { Location } from '@angular/common';
import { environment } from '../../environments/environment';
import { ActivatedRoute } from '@angular/router';
import { CommonService } from '../_services/common.service';
import { BlockUI, NgBlockUI } from 'ng-block-ui';
import { Timer } from 'src/app/_models/timer';
import { ConfirmService } from 'src/app/_helpers/confirm-dialog/confirm.service';
import { NgxSmartModalService, NgxSmartModalComponent } from 'ngx-smart-modal';
import { ToastrService } from 'ngx-toastr';
import { ResponseStatus } from 'src/app/_models/enum';
import { Subscription } from 'rxjs';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { AuthenticationService } from '../_services/authentication.service';
import { trigger, transition, style, animate } from '@angular/animations';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { Page } from '../_models/page';
import { map } from 'rxjs/operators';
import { PaginationService } from '../shared/pagination/pagination.service';

@Component({
  selector: 'app-evaluation-test',
  templateUrl: './evaluation-test.component.html',
  styleUrls: ['./evaluation-test.component.css'],
  animations: [
    trigger('inOutAnimation', [
      transition(':enter', [
        style({ height: 0, opacity: 0 }),
        animate('0.5s ease-out', style({ height: 50, opacity: 1 })),
      ]),
      transition(':leave', [
        style({ height: 50, opacity: 1 }),
        animate('0.5s ease-in', style({ height: 0, opacity: 0 })),
      ]),
    ]),
  ],
})
export class EvaluationTestComponent implements OnInit {
  private window: Window;
  baseUrl = environment.baseUrl;
  mediaBaseUrl = environment.mediaBaseUrl;
  private quiz: any;
  examId: string;
  traineeEvaluationExamId: string;
  definitions: any[];
  mqAnswered: boolean = false;
  changeScreenCounter: number = 0;
  pagedetail: any;
  quizRunning: boolean = false;
  questionList: Array<any> = [];
  qIndex: number = -1;
  btnNextText: string = 'Next';
  noOfAnsweredQs: number = 0;
  mcqList: Array<any> = [];
  timer: Timer = new Timer();
  result: any;
  terminated = null;
  resultList: Array<any> = [];
  resultModal: NgxSmartModalComponent;
  resultDetailModal: NgxSmartModalComponent;
  @ViewChild(TemplateRef, { static: false }) tpl: TemplateRef<any>;
  @BlockUI() blockUI: NgBlockUI;
  currentTime: Date;
  examTime: Date;
  allowAnswerSubmit: boolean = false;
  timerSubscription: Subscription;
  examTimeCheckerInterval: any;
  notAllowMessage: string;
  interval: any;
  isConnected = true;
  page = new Page();
  answerStatus = '';
  feedBackQuestionList: Array<any> = [];
  modalFeedBackQuestionList: Array<any> = [];
  modalRef: BsModalRef;
  modalLgConfig: any = { class: 'gray modal-lg', backdrop: 'static' };
  modalConfig: any = { class: 'gray', backdrop: 'static' };
  template: TemplateRef<any>;
  isSubmitting = false;
  popStateHandler: any;
  answerSubmitted: boolean = false;

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private _service: CommonService,
    public ngxSmartModalService: NgxSmartModalService,
    private toastr: ToastrService,
    private confirmService: ConfirmService,
    private authService: AuthenticationService,
    private route: ActivatedRoute,
    private pageService: PaginationService,
    private modalService: BsModalService,
    private _location: Location
  ) {
    this.examId = this.route.snapshot.paramMap.get('examId');
    this.baseUrl = environment.baseUrl;
    this.window = this.document.defaultView;
    this.page.startsFrom = 1;
    this.page.pageNumber = 1;
    this.page.size = 1;
  }
  ngOnInit(): void {
    this.answerSubmitted = false;
    this.route.params.subscribe((params) => {});
    this.getEvaluationTestDetails();
  }
  ngAfterViewInit() {
    if (!this.isSubmitting) {
      // window.addEventListener('popstate', (event) => {
      //   this.quizRunning ? alert("Your exam has been terminated!") : true;
      //   this.quizRunning ? this.saveAnswer(this.template, true) : true;
      // });
      this.popStateHandler = (event) => {
        if (this.quizRunning) {
          alert('Your exam has been terminated!');
          this.saveAnswer(this.template);
        }
      };
      window.addEventListener('popstate', this.popStateHandler);
    }
  }

  ngOnDestroy() {
    if (this.popStateHandler) {
      window.removeEventListener('popstate', this.popStateHandler);
    }
    if (this.timerSubscription) this.timerSubscription.unsubscribe();
    if (this.interval) clearInterval(this.interval);
  }

  getHourMint(duration: number) {
    return Math.floor(duration / 60) + 'h ' + (duration % 60) + 'm';
  }

  getEvaluationTestDetails() {
    this.blockUI.start('Getting data...');
    this._service
      .get('evaluation-exam/get-exam-info/' + this.examId)
      .subscribe({
        next: (res: any) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
            this._location.back();
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, 'Error!', {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            this._location.back();
            return;
          }
          this.pagedetail = res.Data;
          this.notAllowMessage = res.Message;
        },
        error: (err) => {
          this.toastr.warning(err.Messaage || err, 'Error!', {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          this.blockUI.stop();
        },
        complete: () => this.blockUI.stop(),
      });
  }

  startQuiz(template: TemplateRef<any>) {
    this.blockUI.start('Starting exam. Please wait...');
    this.template = template;
    this._service
      .get('evaluation-exam/get-questions/' + this.examId)
      .subscribe({
        next: (res: any) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, 'Error!', {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.questionList = [];
          this.quiz = { StartDate: new Date().toLocaleString() };
          this.traineeEvaluationExamId = res.Data.TraineeEvaluationExamId;
          res.Data.MCQs.forEach((element) => {
            this.questionList.push({
              Id: element.Id,
              Question: element.Question,
              Options: [
                { Text: element.Option1, Selected: false },
                { Text: element.Option2, Selected: false },
                { Text: element.Option3, Selected: false },
                { Text: element.Option4, Selected: false },
              ],
              Mark: element.Mark,
              Type: 'MCQ',
            });
          });

          res.Data.TrueFalsQs.forEach((x) => {
            this.questionList.push({
              Id: x.Id,
              Question: x.Question,
              Mark: x.Mark,
              Answer: false,
              CorrectAnswer: null,
              Type: 'TFQ',
            });
          });

          res.Data.FIGQs.forEach((x) => {
            this.questionList.push({
              Id: x.Id,
              Question: x.Question,
              Mark: x.Mark,
              Answer: null,
              Type: 'FIGQ',
            });
          });

          if (res.Data.MatchingQs) {
            this.questionList.push({
              LeftSides: res.Data.MatchingQs.LeftSides,
              RightSides: res.Data.MatchingQs.RightSides,
              Type: 'LRMQ',
            });
          }

          res.Data.WrittenQs.forEach((x) => {
            this.questionList.push({
              Id: x.Id,
              Question: x.Question,
              Mark: x.Mark,
              Answer: null,
              Type: 'WQ',
            });
          });

          this.qIndex = 0;
          this.quizRunning = true;
          this.allowAnswerSubmit = true;
          // this.preventWindowScreenChanges();
          if (this.allowAnswerSubmit) {
            this.examTimeChangeScreen(template);
          }
          this.timerSubscription = this.timer
            .start(this.pagedetail.DurationMnt * 60)
            .subscribe((status) => {
              if (status === 'ended') {
                this.onTimesUp(template);
                this.timerSubscription.unsubscribe();
              }
            });
        },
        error: (err) => {
          this.toastr.error(err.message || err, 'Error!', {
            closeButton: true,
            disableTimeOut: true,
            enableHtml: true,
          });
          this.blockUI.stop();
        },
        complete: () => this.blockUI.stop(),
      });
  }
  onPageChange(page: number) {
    this.setPage(page);
  }
  drop(event: CdkDragDrop<string[]>) {
    this.mqAnswered = true;
    moveItemInArray(
      this.questionList[this.qIndex].RightSides,
      event.previousIndex,
      event.currentIndex
    );
  }

  setPage(pageNumber) {
    this.page.pageNumber = pageNumber;
    this.qIndex = pageNumber - 1;
    this.btnNextText =
      this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';
  }
  onTimesUp(template: TemplateRef<any>) {
    if (this.answerSubmitted) return;
    //  this.saveAnswerSheetIntoLocalStorage(true);
    this.ngxSmartModalService.closeAll();
    this.confirmService.close();

    this.confirmService
      .confirm(
        "Time's Up!!",
        'Your answer will get submitted automatically. ' + this.answerStatus,
        'OK',
        null,
        true
      )
      .subscribe(() => {
        this.answerSubmitted = true;
        this.saveAnswer(template, true)
          .then(() => {
            console.log('Answer saved on time up');
          })
          .catch((error) => {
            console.error('Error saving answer on time up:', error);
          });
      });
  }
  onScreenChange(template: TemplateRef<any>) {
    //  this.saveAnswerSheetIntoLocalStorage(true);
    if (this.answerSubmitted) return;
    this.ngxSmartModalService.closeAll();
    this.confirmService.close();

    if (!this.isSubmitting) {
      this.confirmService
        .confirm(
          'Your exam has been terminated!',
          'Your answer will get submitted automatically. ' + this.answerStatus,
          'OK',
          null,
          true
        )
        .subscribe(() => {
          this.answerSubmitted = true;
          this.saveAnswer(template, true)
            .then(() => {
              console.log('Answer saved on screen change');
            })
            .catch((error) => {
              console.error('Error saving answer on screen change:', error);
            });
        });
    }
  }

  nextQuestion(template: TemplateRef<any>) {
    if (this.qIndex < this.questionList.length - 1) this.qIndex++;
    else {
      this.timer.pause();
      this.submitAnswer(template);
      return;
    }
    this.btnNextText =
      this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';
    this.page.pageNumber = this.qIndex + 1;
    this.pageService.currentPage.next(this.page.pageNumber);
  }

  prevQuestion() {
    if (this.qIndex !== 0) this.qIndex--;
    this.page.pageNumber = this.page.pageNumber - 1;
    this.pageService.currentPage.next(this.page.pageNumber);
  }

  submitAnswer(template: TemplateRef<any>) {
    if (this.answerSubmitted) return;
    this.confirmService
      .confirm(
        'Are you sure?',
        'You are going to submit answer. ' + this.answerStatus,
        'Yes, Submit Answer'
      )
      .subscribe((result) => {
        if (result) {
          this.timer.pause();
          this.answerSubmitted = true;
          this.saveAnswer(template)
            .then(() => {
              return this.saveActivity(this.pagedetail.ExamName, 'Exam');
            })
            .catch((error) => {
              console.error('Error in exam submission sequence:', error);
            });
        } else {
          this.timer.resume();
        }
      });
  }

  private saveAnswer(
    template: TemplateRef<any>,
    autoSubmission: boolean = false
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.isConnected) {
        this.toastr.error('You have no internet connection', 'ALERT!', {
          timeOut: 4000,
        });
        this.timer.resume();
        reject('No internet connection');
        return;
      }
      let submittedMCQList = [],
        submittedTFQList = [],
        submittedFIGQList = [],
        submittedLRMQList = [],
        submittedWQList = [];
      this.questionList
        .filter((x) => x.Type === 'MCQ')
        .forEach((element) => {
          submittedMCQList.push({
            QuestionId: element.Id,
            Answered: element.Options.map(function (x, i) {
              if (x.Selected) return i + 1;
              else return 0;
            })
              .filter((x) => x > 0)
              .join(),
          });
        });
      this.questionList
        .filter((x) => x.Type === 'TFQ')
        .forEach((element) => {
          submittedTFQList.push({
            QuestionId: element.Id,
            Answered: element.Answer,
            CorrectAnswer: !element.Answer
              ? element.CorrectAnswer
                ? element.CorrectAnswer.trim()
                : element.CorrectAnswer
              : null,
          });
        });
      this.questionList
        .filter((x) => x.Type === 'FIGQ')
        .forEach((element) => {
          submittedFIGQList.push({
            QuestionId: element.Id,
            Answered: element.Answer ? element.Answer.trim() : element.Answer,
          });
        });

      let mQuestions = this.questionList.find((x) => x.Type === 'LRMQ');
      if (mQuestions)
        for (let i = 0; i < mQuestions.LeftSides.length; i++) {
          const element = mQuestions.LeftSides[i];
          submittedLRMQList.push({
            QuestionId: element.Id,
            Answered: mQuestions.RightSides[i],
          });
        }

      this.questionList
        .filter((x) => x.Type === 'WQ')
        .forEach((element) => {
          submittedWQList.push({
            QuestionId: element.Id,
            Answered: element.Answer ? element.Answer.trim() : element.Answer,
          });
        });

      const obj = {
        TraineeEvaluationExamId: this.traineeEvaluationExamId,
        ExamId: this.examId,
        StartTime: this.quiz.StartDate,
        EndTime: new Date().toLocaleString(),
        AutoSubmission: autoSubmission,
        MCQList: submittedMCQList,
        TFQList: submittedTFQList,
        FIGQList: submittedFIGQList,
        MatchingQList: submittedLRMQList,
        WQList: submittedWQList,
        Terminated: this.terminated,
      };

      this.isSubmitting = true;
      this.blockUI.start('Submitting answer. Please wait...');

      this._service.post('evaluation-exam/save-answers', obj).subscribe({
        next: (res: any) => {
          this.quizRunning = false;
          this.isSubmitting = false;
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.error(res.Message, 'Warning!', {
              closeButton: true,
              disableTimeOut: true,
              enableHtml: true,
            });
            // this.timer.resume();
            this._location.back();
            reject(res.Message);
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, 'Error!', {
              closeButton: true,
              disableTimeOut: true,
              enableHtml: true,
            });
            // this.timer.resume();
            this._location.back();
            reject(res.Message);
            return;
          }

          this.toastr.success(res.Message, 'Answer Submission!', {
            timeOut: 4000,
          });
          this.allowAnswerSubmit = false;
          if (!res.Data) this._location.back();
          else {
            this.result = res.Data;
            this.modalRef = this.modalService.show(template, this.modalConfig);
          }
          resolve(res);
        },
        error: (err) => {
          this.quizRunning = false;
          this.isSubmitting = false;
          this.timer.resume();
          this.toastr.error(err.message || err, 'Error!', {
            closeButton: true,
            disableTimeOut: true,
            enableHtml: true,
          });
          this.blockUI.stop();
          this._location.back();
          reject(err);
        },
        complete: () => {
          this.isSubmitting = false;
          this.blockUI.stop();
        },
      });
    });
  }

  backClicked() {
    if (this.quizRunning) {
      this.saveAnswer(this.template, true)
        .then(() => {
          console.log('Answer saved on back click');
        })
        .catch((error) => {
          console.error('Error saving answer on back click:', error);
        });
    } else {
      this._location.back();
    }
  }
  selectSpecific(lists) {
    lists.map((x) => (x.Selected = false));
  }
  openFeedBackModal(template: TemplateRef<any>) {
    this.modalFeedBackQuestionList = [];
    if (this.feedBackQuestionList.length === 0) {
      this.blockUI.start('Getting feedback questions. Pleae wait...');
      this._service
        .get('evaluation-exam/get-feedback-questions/' + this.pagedetail.Id)
        .subscribe({
          next: (res: any) => {
            if (res.Status === ResponseStatus.Warning) {
              this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
              return;
            } else if (res.Status === ResponseStatus.Error) {
              this.toastr.error(res.Message, 'Error!', { timeOut: 2000 });
              return;
            }
            this.feedBackQuestionList = res.Data;
            this.feedBackQuestionList.forEach((group) => {
              group.Questions.forEach((element) => {
                if (element.QuestionType !== 'Checkbox')
                  element.Answers =
                    element.Answers.length > 0 ? element.Answers.join() : null;
                if (element.QuestionType === 'Rating')
                  element.Answers = element.Answers
                    ? Number(element.Answers)
                    : null;
              });
            });
            this.modalFeedBackQuestionList = this.feedBackQuestionList;
          },
          error: (err) => {
            this.toastr.warning(err.Messaage || err, 'Warning!', {
              closeButton: true,
              disableTimeOut: false,
            });
            this.blockUI.stop();
          },
          complete: () => {
            this.blockUI.stop();
          },
        });
    } else {
      this.modalFeedBackQuestionList = this.feedBackQuestionList;
    }
    this.modalRef = this.modalService.show(template, this.modalLgConfig);
  }

  modalHideFeedBack() {
    this.modalFeedBackQuestionList = [];
    this.modalRef.hide();
  }
  onChangeCheckBox(event, question, option) {
    if (event.target.checked) {
      question.Answers.push(option);
    } else question.Answers.splice(question.Answers.indexOf(option), 1);
  }

  onSubmitFeedback() {
    let feedbacks = [];
    this.modalFeedBackQuestionList.forEach((group) => {
      group.Questions.forEach((element) => {
        if (element.QuestionType !== 'Checkbox')
          element.Answers = element.Answers ? [element.Answers.toString()] : [];
        feedbacks.push({ QuestionId: element.Id, Answers: element.Answers });
      });
    });

    const obj = {
      ExamId: this.pagedetail.Id,
      Feedbacks: feedbacks,
    };

    this.blockUI.start('Saving feedbacks. Please wait...');
    this._service.post('feedback/learning-hour/save-or-update', obj).subscribe({
      next: (res: any) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, 'Error!', { timeOut: 2000 });
          return;
        }
        this.toastr.success(res.Message, 'SUCCESS!', { timeOut: 2000 });
        this.feedBackQuestionList = [];
        this.pagedetail.FeedbackGiven = true;
      },
      error: (err) => {
        this.toastr.warning(err.Messaage || err, 'Warning!', {
          closeButton: true,
          disableTimeOut: false,
        });
        this.blockUI.stop();
      },
      complete: () => {
        this.blockUI.stop();
        this.modalHideFeedBack();
      },
    });
  }

  // preventWindowScreenChanges()
  // {
  //   this.window.addEventListener("beforeunload", function (e) {
  //     var confirmationMessage = 'It looks like you have not finishes the exam yet! '
  //                             + 'If you leave before finishing, your exam will be expired.';

  //     (e || window.event).returnValue = confirmationMessage; //Gecko + IE
  //     // this.alert();
  //     return confirmationMessage; //Gecko + Webkit, Safari, Chrome etc.
  // });
  // }
  examTimeChangeScreen(template: TemplateRef<any>) {
    document.addEventListener('visibilitychange', (event) => {
      if (document.visibilityState != 'visible') {
        if (this.changeScreenCounter === 0 && this.allowAnswerSubmit == true) {
          alert(
            'You are not allowed to change the screen!!! If you try one more step to change the screen the exam will be terminated!!!'
          );
          this.changeScreenCounter++;
        } else if (
          this.changeScreenCounter === 1 &&
          this.allowAnswerSubmit == true
        ) {
          this.terminated = true;
          this.onScreenChange(template);
          this.timerSubscription.unsubscribe();
          this.quizRunning = false;
          this.changeScreenCounter++;
          this.allowAnswerSubmit;
        }
      }
    });
  }
  modalHide() {
    this.modalRef.hide();
    this._location.back();
  }
  saveActivity(title: string, type: string): Promise<any> {
    return new Promise((resolve, reject) => {
      let obj = {
        title: title,
        contentType: type,
      };
      this.blockUI.start('Saving activities. Please wait...');
      this._service
        .get('open-material/save-trainee-evaluation-activity', obj)
        .subscribe({
          next: (res: any) => {
            if (res.Status === ResponseStatus.Warning) {
              this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
              reject(res.Message);
              return;
            } else if (res.Status === ResponseStatus.Error) {
              this.toastr.error(res.Message, 'Error!', {
                closeButton: true,
                disableTimeOut: false,
                enableHtml: true,
              });
              reject(res.Message);
              return;
            }
            resolve(res);
          },
          error: (err) => {
            this.blockUI.stop();
            // Don't show error for activity tracking - it's not critical for exam submission
            console.log('Activity tracking failed (non-critical):', err);
            // Don't reject for activity tracking errors - just resolve to continue the flow
            resolve(null);
            // this.toastr.warning(err.Messaage || err, 'Error!', {
            //   closeButton: true,
            //   disableTimeOut: false,
            //   enableHtml: true,
            // });
          },
          complete: () => {
            this.blockUI.stop();
          },
        });
    });
  }
}
