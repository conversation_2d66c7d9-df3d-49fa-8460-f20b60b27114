﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/evaluation-exam")]
    public class EvaluationExamController : ApplicationController, IDisposable
    {
        private readonly IEvaluationExamService _service;
        private bool _disposed = false;

        public EvaluationExamController()
        {
            _service = new EvaluationExamService();
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> CertificationTestCreateOrUpdate()
        {
            try
            {
                var model = Newtonsoft.Json.JsonConvert.DeserializeObject<EvaluationExamModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                using (var _nservice = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
                {
                    return Ok(await _nservice.EvaluationExamCreateOrUpdate(model, User.Identity));
                }
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetExamDropDownList()
        {
            return Ok(await _service.GetExamDropDownList());
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("publish/dropdown-list")]
        public async Task<IHttpActionResult> GetCertificatePublishTestDropDownList()
        {
            return Ok(await _service.GetPublishExamDropDownList());
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetEvaluationExamById(Guid id)
        {
            return Ok(await _service.GetEvaluationExamById(id));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetEvaluationExamList(long? categoryId, int size, int pageNumber)
        {
            return Ok(await _service.GetEvaluationExamList(categoryId, size, pageNumber));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("trainee-wise-exam")]
        public async Task<IHttpActionResult> GetTraineeWiseTestList(int size, int pageNumber)
        {
            using (var service = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await service.GetTraineeWiseTestList(size, pageNumber, CurrentUser));
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-mcq-list/{examId}")]
        public async Task<IHttpActionResult> GetMCQQuestionList(Guid examId)
        {
            return Ok(await _service.GetMCQQuestionList(examId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("get-true-false-list/{examId}")]
        public async Task<IHttpActionResult> GetTrueFalseQuestionList(Guid examId)
        {
            return Ok(await _service.GetTrueFalseQuestionList(examId));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-fill-in-the-gap-list/{examId}")]
        public async Task<IHttpActionResult> GetFIGQuestionList(Guid examId)
        {
            return Ok(await _service.GetFIGQuestionList(examId));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-matching-list/{examId}")]
        public async Task<IHttpActionResult> GetMatchingQuestionList(Guid examId)
        {
            return Ok(await _service.GetMatchingQuestionList(examId));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-written-list/{examId}")]
        public async Task<IHttpActionResult> GetWrittenQuestionList(Guid examId)
        {
            return Ok(await _service.GetWrittenQuestionList(examId));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("get-mcq-list-excel/{examId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetMCQQuestionListExcel(Guid examId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetMCQQuestionListExcel(examId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-true-false-list-excel/{examId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetTrueFalseQuestionListExcel(Guid examId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetTrueFalseQuestionListExcel(examId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-fill-in=the=gap-list-excel/{examId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetFIGQuestionListExcel(Guid examId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetFIGQuestionListExcel(examId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-matching-list-excel/{examId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetMatchingQuestionListExcel(Guid examId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetMatchingQuestionListExcel(examId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-written-list-excel/{examId}/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetWrittenQuestionListExcel(Guid examId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetWrittenQuestionListExcel(examId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("delete-mcq/{id}")]
        public async Task<IHttpActionResult> DeleteMCQById(long id)
        {
            return Ok(await _service.DeleteQuestionById(id, QuesType.MCQ));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("get-trainee-exam-list")]
        public async Task<IHttpActionResult> GetTraineeExamList(Guid examId, ExamStatus? examStatus, Guid? traineeId, int size, int pageNumber)
        {
            return Ok(await _service.GetTraineeExamList(examId, examStatus, traineeId, size, pageNumber));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("get-unpublished-trainee-exam-list/{examId}")]
        public async Task<IHttpActionResult> GetUnpublishedTraineeExamList(Guid examId)
        {
            return Ok(await _service.GetUnpublishedTraineeExamList(examId));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-trainee-answer-sheet/{id}")]
        public async Task<IHttpActionResult> GetAnswerSheetForTrainee(Guid id)
        {
            return Ok(await _service.GetAnswerSheetForTrainee(id));
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("save-marking")]
        public async Task<IHttpActionResult> SaveExamMarking(ExamMarkingModel model)
        {
            using (var _nservice = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.SaveExamMarking(model, CurrentUser));
            }
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("upload-marking-file")]
        public async Task<IHttpActionResult> UploadTraineeAnswersheet()
        {
            using (var _nservice = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.UploadTraineeAnswersheet(CurrentUser));
            }
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("publish-trainee-exam")]
        public async Task<IHttpActionResult> PublishTraineeEvaluationExam(List<Guid> traineeExamIds)
        {
            using (var _nservice = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.PublishTraineeExam(traineeExamIds, CurrentUser));
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("download-sample-question")]
        public HttpResponseMessage DownloadSampleMCQ(QuesType quesType)
        {
            string filePath = string.Empty;
            switch (quesType)
            {
                case QuesType.MCQ:
                    filePath = Generator.UploadSampleMCQ;
                    break;
            }
            if (!File.Exists(filePath))
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }
            else
            {
                HttpResponseMessage result = Request.CreateResponse(HttpStatusCode.OK);
                result.Content = new StreamContent(File.OpenRead(filePath));
                result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                return result;
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("download-trainee-answersheet-excel")]
        public async Task<HttpResponseMessage> GetTraineeAnswersheetExcel(Guid examId, string status)
        {
            byte[] byteArray = await _service.GetTraineeAnswersheetExcel(examId, status);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }


        [Authorize(Roles = "Admin"), HttpGet, Route("download-list-in-excel")]
        public async Task<HttpResponseMessage> GetTraineeExamExcel(Guid examId, ExamStatus? examStatus, Guid? traineeId, int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetTraineeExamExcel(examId, examStatus, traineeId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-answer-sheet-in-pdf/{id}")]
        public async Task<HttpResponseMessage> GetAnswerSheetPDF(Guid id)
        {
            byte[] byteArray = await _service.GetAnswerSheetPDF(id);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
            //    return response;
            return response;

        }


        #region Trainee Panel APIs
        [Authorize(Roles = "Trainee"), HttpGet, Route("get-exam-info/{id}")]
        public async Task<IHttpActionResult> GetEvaluationExamInfoById(Guid id)
        {
            using (var _nservice = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.GetEvaluationExamInfoById(id, CurrentUser));
            }
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-questions/{id}")]
        public async Task<IHttpActionResult> GetExamQuestions(Guid id)
        {
            using (var _nservice = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.GetExamQuestions(id, CurrentUser));
            }
        }

        [Authorize(Roles = "Trainee"), HttpPost, Route("save-answers")]
        public async Task<IHttpActionResult> SaveEvaluationExamAnswer(ExamAnserSaveModel model)
        {
            using (var _nservice = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.SaveEvaluationExamAnswer(model, CurrentUser));
            }
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-evaluation-test-results")]
        public async Task<IHttpActionResult> GetEvaluationExamResults(string name, long? categoryId, int size, int pageNumber)
        {
            using (var _nservice = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                return Ok(await _nservice.GetEvaluationExamResults(name, categoryId, size, pageNumber, CurrentUser));
            }
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-feedback-questions/{id}")]
        public async Task<IHttpActionResult> GetFeedbackQuestions(Guid id)
        {
            return Ok(await _service.GetFeedbackQuestions(id, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-my-evaluation-test-results")]
        public async Task<HttpResponseMessage> GetEvaluationTestResultReport(int timeZoneOffset, ReportType reportType)
        {
            try
            {
                byte[] byteArray = reportType == ReportType.Excel ? await _service.GetExamResultExcelForTrainee(timeZoneOffset, CurrentUser) : await _service.GetExamResultPdfForTrainee(timeZoneOffset, CurrentUser);
                //Create a new response.
                var response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    //Assign byte array to response content.
                    Content = new ByteArrayContent(byteArray)
                };

                switch (reportType)
                {
                    case ReportType.Pdf:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                        break;
                    case ReportType.Excel:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                        break;
                }
                //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.

                //    return response;
                return response;
            }
            catch (Exception)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
        }
        #endregion

        [Authorize(Roles = "Admin"), HttpGet, Route("get-exam-progress-report-excel")]
        public async Task<HttpResponseMessage> GetExamProgressReportExcel(Guid? examId, long? divisionId, DateTime? startDate, DateTime? endDate, ReportType reportType)
        {
            using (var _nservice = new EvaluationExamService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser))
            {
                byte[] byteArray = reportType == ReportType.Excel ? await _nservice.GetExamProgressReportExcel(examId, divisionId, startDate, endDate) : await _nservice.GetExamProgressReportPdf(examId, divisionId, startDate, endDate);
                //Create a new response.
                var response = new HttpResponseMessage(HttpStatusCode.OK);
                //Assign byte array to response content.
                response.Content = new ByteArrayContent(byteArray);

                //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
                //response.Content.Headers.Add("x-filename", "Student_List.xls");
                //Set MIME type.
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                //    return response;
                return response;
            }

        }

        #region IDisposable Implementation
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _service?.Dispose();
                }

                // Dispose unmanaged resources (if any)

                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        ~EvaluationExamController()
        {
            Dispose(false);
        }
        #endregion

    }
}
