﻿using Brac.LMS.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public enum ExamType { MockTest, CertificationTest }

    public class CourseExam : AuditableEntity
    {
        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }

        //public string ExamName { get; set; }
        public string ExamInstructions { get; set; }
        public int Quota { get; set; }
        public int Marks { get; set; }
        public int DurationMnt { get; set; }
        public bool MCQOnly { get; set; }
        public bool Random { get; set; }
        public bool Publish { get; set; }
        //public int ExamMCQNo { get; set; }
        //public int ExamTrueFalseNo { get; set; }
        //public int ExamFIGNo { get; set; }
        //public int ExamMatchingNo { get; set; }
        //public int ExamWritingNo { get; set; }

        private DateTime? _StartDate;
        public DateTime? StartDate
        {
            get
            { return _StartDate; }
            set
            { _StartDate = value.HasValue ? value.Value.ToKindLocal() : value; }
        }


        private DateTime? _EndDate;
        public DateTime? EndDate
        {
            get
            { return _EndDate; }
            set
            { _EndDate = value.HasValue ? value.Value.ToKindLocal() : value; }
        }
        public virtual ICollection<TraineeExamAttempt> TraineeAttempts { get; set; }
        public virtual ICollection<MCQQuestion> MCQQuestions { get; set; }
        public virtual ICollection<TrueFalseQuestion> TrueFalseQuestions { get; set; }
        public virtual ICollection<FIGQuestion> FIGQuestions { get; set; }
        public virtual ICollection<MatchingQuestion> MatchingQuestions { get; set; }
        public virtual ICollection<WrittenQuestion> WrittenQuestions { get; set; }
        public virtual ICollection<SegmentWiseQsSetup> SegmentWiseQsSetups { get; set; }
    }

    public class TraineeExamAttempt
    {
        [Key, Column(Order = 1)]
        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        [Key, Column(Order = 2)]
        public Guid ExamId { get; set; }
        public virtual CourseExam Exam { get; set; }

        public int Attempt { get; set; }
        public int ExtendedQuota { get; set; }
    }

    public class SegmentWiseQsSetup
    {
        [Key, Column(Order = 1)]
        public Guid ExamId { get; set; }
        public virtual CourseExam Exam { get; set; }


        [Key, Column(Order = 2)]
        public long SegmentId { get; set; }
        public virtual CourseSegment Segment { get; set; }


        public int NoOfMCQ { get; set; }
        public int NoOfTrueFalse { get; set; }
        public int NoOfFIG { get; set; }
        public int NoOfMatching { get; set; }
        public int NoOfWriting { get; set; }
    }
}
