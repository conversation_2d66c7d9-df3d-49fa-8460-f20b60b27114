﻿
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Brac.LMS.ViewModels;
using Microsoft.AspNet.Identity;

using Newtonsoft.Json;

using SendEmailApp;

using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;

using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;

namespace Brac.LMS.App.Services
{
    public class AccountService : IAccountService
    {
        private readonly AuditLogHelper _auditLogHelper ;
        private readonly ApplicationDbContext _context;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
      
        public AccountService()
        {
            _context = new ApplicationDbContext();
        }
        public AccountService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        public AccountService(string controller, string method)
        {
            _context = new ApplicationDbContext();
            _auditLogHelper = new AuditLogHelper(controller, method);
        }
        public async Task<APIResponse> GetUserList(string text, int size, int pageNumber, IIdentity identity)
        {
            try
            {
                var query = _context.Users.Where(x => x.UserName != identity.Name && x.Email != "<EMAIL>" && (x.UserType == UserType.Admin || x.UserType == UserType.Both));

                if (!string.IsNullOrEmpty(text)) query = query.Where(x => x.UserName.Contains(text) || x.FirstName.Contains(text) || x.LastName.Contains(text));
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderBy(x => x.FirstName)
                  .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery
                    .Join(_context.Trainees, x => x.Id, y => y.UserId, (x, y) => new
                    {
                        User = x,
                        Trainee = y
                    })
                    .Select(x => new { x.User.Id, x.User.UserName, x.User.FirstName, x.User.LastName, x.User.Active, x.User.LastLogOn, x.Trainee.PIN, Department = x.Trainee.Department.Name, Division = x.Trainee.Division.Name, x.Trainee.PhoneNo })
                    .ToListAsync();
                var count = await ((!string.IsNullOrEmpty(text)) ? filteredQuery.CountAsync() : _context.Users.CountAsync());

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }

        public async Task<APIResponse> GetUserById(string id, ApplicationUserManager userManager)
        {
            try
            {

                var item = await userManager.FindByIdAsync(id);
                if (item == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "User not found by ID"
                    };

                var roles = await userManager.GetRolesAsync(id);
                var data = new { item.Id, item.Email, item.UserGroupId, item.Active, item.FirstName, item.LastName, item.LastLogOn, item.PhoneNumber, Roles = roles.ToArray() };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> GetAdminUserById(string id, ApplicationUserManager userManager)
        {
            try
            {
                var user = await userManager.Users.Where(x => x.Id == id).Select(x => new { x.Id, x.Active }).FirstOrDefaultAsync();
                if (user == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "User not found by ID"
                };

                var trainee = await _context.Trainees.Where(x => x.UserId == id).Select(x => new
                {
                    x.Id,
                    x.Name,
                    x.User.ImagePath,
                    x.UserId,
                    x.PhoneNo,
                    x.PIN
                }).FirstOrDefaultAsync();
                if (trainee == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Trainee info not found by ID"
                    };

                var roles = await userManager.GetRolesAsync(id);
                var data = new { Trainee = trainee, user.Active, Roles = roles.ToArray() };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> CreateOrUpdateUser(AccountModel model, ApplicationUserManager userManager)
        {
            bool isEdit = true;
            try
            {
                ApplicationUser user;
                if (!string.IsNullOrEmpty(model.Id))
                {
                    user = await userManager.FindByIdAsync(model.Id);
                    if (user == null)
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "User not found"
                        };
                }
                else
                {
                    user = new ApplicationUser { UserName = model.Email, Email = model.Email, UserType = UserType.Admin };
                    isEdit = false;
                }

                user.FirstName = model.FirstName;
                user.LastName = model.LastName;
                user.UserGroupId = model.UserGroupId;
                if (user.Active && !model.IsActive) user.DeActivateOn = DateTime.UtcNow;
                user.Active = model.IsActive;

                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    try
                    {
                        IdentityResult result;
                        if (isEdit)
                        {
                            result = await userManager.UpdateAsync(user);
                            if (!result.Succeeded)
                            {
                                return new APIResponse
                                {
                                    Status = ResponseStatus.Warning,
                                    Message = string.Join(", ", result.Errors)
                                };
                            }

                            var existingRoles = await userManager.GetRolesAsync(user.Id);
                            if (existingRoles.Any())
                            {
                                result = await userManager.RemoveFromRolesAsync(user.Id, existingRoles.ToArray());
                                if (!result.Succeeded)
                                {
                                    return new APIResponse
                                    {
                                        Status = ResponseStatus.Warning,
                                        Message = string.Join(", ", result.Errors)
                                    };
                                }
                            }

                            if (model.Roles.Any())
                            {
                                result = await userManager.AddToRolesAsync(user.Id, model.Roles);
                                if (!result.Succeeded)
                                {
                                    return new APIResponse
                                    {
                                        Status = ResponseStatus.Warning,
                                        Message = string.Join(", ", result.Errors)
                                    };
                                }
                            }
                        }
                        else
                        {
                            result = await userManager.CreateAsync(user, model.Password);
                            if (!result.Succeeded)
                            {
                                return new APIResponse
                                {
                                    Status = ResponseStatus.Warning,
                                    Message = string.Join(", ", result.Errors)
                                };
                            }

                            if (model.Roles.Any())
                            {
                                result = await userManager.AddToRolesAsync(user.Id, model.Roles);
                                if (!result.Succeeded)
                                {
                                    return new APIResponse
                                    {
                                        Status = ResponseStatus.Warning,
                                        Message = string.Join(", ", result.Errors)
                                    };
                                }
                            }
                        }
                        scope.Complete();
                    }
                    catch (Exception ex)
                    {
                        scope.Dispose();
                        return new APIResponse
                        {
                            Status = ResponseStatus.Error,
                            Message = ex.Message
                        };
                    }
                }

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> CreateOrUpdateAdminUser(AdminAccountModel model, ApplicationUserManager userManager)
        {
            bool isEdit = true;
            try
            {
                ApplicationUser user = await userManager.FindByIdAsync(model.UserId);
                if (user == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "User not found"
                    };

                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    try
                    {
                        IdentityResult result;

                        var restrictedRoles = new string[] { "Admin", "Trainee" };

                        var existingRoles = await userManager.GetRolesAsync(user.Id);
                        existingRoles = existingRoles.Where(x => !restrictedRoles.Contains(x)).ToList();

                        if (existingRoles.Any())
                        {
                            result = await userManager.RemoveFromRolesAsync(user.Id, existingRoles.ToArray());
                            if (!result.Succeeded)
                            {
                                return new APIResponse
                                {
                                    Status = ResponseStatus.Warning,
                                    Message = string.Join(", ", result.Errors)
                                };
                            }
                        }

                        if (!model.IsActive)
                        {
                            user.UserType = UserType.Trainee;
                            result = await userManager.RemoveFromRoleAsync(user.Id, "Admin");
                            if (!result.Succeeded)
                            {
                                return new APIResponse
                                {
                                    Status = ResponseStatus.Warning,
                                    Message = string.Join(", ", result.Errors)
                                };
                            }
                        }
                        else
                        {
                            if (!await userManager.IsInRoleAsync(user.Id, "Admin"))
                                model.Roles.Add("Admin");

                            if (model.Roles.Any())
                            {
                                result = await userManager.AddToRolesAsync(user.Id, model.Roles.ToArray());
                                if (!result.Succeeded)
                                {
                                    return new APIResponse
                                    {
                                        Status = ResponseStatus.Warning,
                                        Message = string.Join(", ", result.Errors)
                                    };
                                }
                            }

                            user.UserType = UserType.Both;
                        }

                        result = await userManager.UpdateAsync(user);
                        if (!result.Succeeded)
                        {
                            return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = string.Join(", ", result.Errors)
                            };
                        }

                        scope.Complete();
                    }
                    catch (Exception ex)
                    {
                        scope.Dispose();
                        return new APIResponse
                        {
                            Status = ResponseStatus.Error,
                            Message = ex.Message
                        };
                    }
                }

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> RegisterUser(AccountModel model, ApplicationUserManager userManager)
        {
            try
            {
                var user = new ApplicationUser() { UserName = model.Email, Email = model.Email, FirstName = model.FirstName, LastName = model.LastName, Active = model.IsActive };

                var result = await userManager.CreateAsync(user, model.Password);
                if (!result.Succeeded)
                {
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = string.Join(", ", result.Errors)
                    };

                }

                result = await userManager.AddToRoleAsync(user.Id, "Admin");
                if (!result.Succeeded)
                {
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = string.Join(", ", result.Errors)
                    };
                }
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully Registered"
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> ResetPassword(string id, ApplicationUserManager userManager)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "id is null"
                    };

                var user = await userManager.FindByIdAsync(id);
                if (user == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "User not found"
                    };

                user.PasswordHash = userManager.PasswordHasher.HashPassword("999999");

                await userManager.UpdateAsync(user);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Reset Successfully"
                    }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> ChangePassword(PasswordChangeModel model, ApplicationUserManager userManager, IIdentity identity)
        {
            try
            {
                if (string.IsNullOrEmpty(model.OldPassword))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Old password is required"
                    };
                if (string.IsNullOrEmpty(model.NewPassword))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "New password is required"
                    };

                var user = await userManager.FindAsync(identity.Name, model.OldPassword); 
                if (user == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Old password not match"
                };

                user.PasswordHash = userManager.PasswordHasher.HashPassword(model.NewPassword);
                await userManager.UpdateAsync(user);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Password Successfully Changed"
                    }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> ForgetPassword(string email, ApplicationUserManager userManager)
        {
            try
            {
                string userUrl, resetPassPath;
                var user = await userManager.Users.FirstOrDefaultAsync(x => x.Email == email);
                if (user == null) return new APIResponse
                {
                    Status = ResponseStatus.Success
                };

                switch (user.UserType)
                {
                    case UserType.Admin:
                        userUrl = System.Configuration.ConfigurationManager.AppSettings["AdminUrl"];
                        resetPassPath = "auth/reset-password";
                        break;
                    case UserType.Trainee:
                        userUrl = System.Configuration.ConfigurationManager.AppSettings["TraineeUrl"];
                        resetPassPath = "reset-password";
                        break;
                    default:
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "Something is wrong. Please try again later."
                        };

                }

                //if (user.LoginType == LoginType.External)
                //    throw new Exception("You are an external user. Please try to reset your password from Active Directory");

                var code = Utility.Convert_StringvalueToHexvalue(user.Email + "|" + DateTime.Now.AddMinutes(10).ToString("dd-MMM-yyyy HH:mm:ss"), Encoding.Unicode);
                var callbackUrl = string.Format("{0}{1}/{2}/{3}", userUrl, resetPassPath, email, code);

                var em = new MailSendService();
                var institute = await _context.Configurations.Select(x => x.Name).FirstOrDefaultAsync();

                using (StreamReader reader = File.OpenText(Generator.EmailTemplatePasswordReset)) // Path to your 
                {                                                         // HTML file
                    StringBuilder sb = new StringBuilder(reader.ReadToEnd());
                    sb = sb.Replace("#link#", callbackUrl);
                    sb = sb.Replace("#institute#", institute);
                    sb = sb.Replace("#account-type#", "E-Learning Solution " + user.UserType.ToString());

                    em.SendMail(email, null, null, "Reset Your E-Learning Solution " + user.UserType.ToString() + " account passowrd", sb.ToString());
                }
                return new APIResponse
                {
                    Status = ResponseStatus.Success,

                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> SetNewPassword(ForgetPasswordModel model, ApplicationUserManager userManager)
        {
            try
            {
                if (string.IsNullOrEmpty(model.Email)) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Email is required"
                };
                if (string.IsNullOrEmpty(model.NewPassword))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "New password is required"
                    };
                if (string.IsNullOrEmpty(model.Code))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Code is required"
                    };

                var authenticators = Utility.Convert_HexvalueToStringvalue(model.Code, Encoding.Unicode).Split('|');

                var expiredDate = DateTime.Parse(authenticators.Last());
                if (DateTime.Now > expiredDate) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "This link has been expired already"
                };

                var user = await userManager.FindByEmailAsync(model.Email); //_context.Users.FirstOrDefault(t => t.Id == model.UserId && t.UserName == model.Email);
                if (user == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "User not found for this email"
                    };

                if (authenticators.First() != user.Email) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Unauthorized request"
                };

                user.PasswordHash = userManager.PasswordHasher.HashPassword(model.NewPassword);
                await userManager.UpdateAsync(user);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Password Successfully Changed"
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> UpdateProfile(UserProfileModel model, IIdentity identity, ApplicationUserManager userManager)
        {
            try
            {
                var user = await userManager.FindByNameAsync(identity.Name);
                if (user == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "User info not found"
                };

                user.FirstName = model.FirstName;
                user.LastName = model.LastName;
                user.PhoneNumber = model.PhoneNumber;
                user.Gender = model.Gender;

                var result = await userManager.UpdateAsync(user);
                if (!result.Succeeded) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Error on user update: " + string.Join(", ", result.Errors)
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully  Updated",
                        user.ImagePath
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> UpdatePhoto(IIdentity identity, ApplicationUserManager userManager)
        {
            try
            {
                var user = await userManager.FindByNameAsync(identity.Name);
                if (user == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "User info not found"
                };

                if (HttpContext.Current.Request.Files.Count > 0)
                    user.ImagePath = Utility.SaveImage(DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + Utility.RandomString(3, false), "/Images/Admin/", HttpContext.Current.Request.Files[0], user.ImagePath, 230, 230);
                else if (HttpContext.Current.Request.Form.Keys[0]=="photo")
                {

                }
                else
                    user.ImagePath = Utility.RemoveImage(user.ImagePath);
                var result = await userManager.UpdateAsync(user);
                if (!result.Succeeded) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Error on user update: " + string.Join(", ", result.Errors)
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = user.ImagePath,
                    Message = user.ImagePath != null ? "Image Uploaded Successfully." : "Image Removed Successfully"
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
        public async Task<APIResponse> GetProfile(IIdentity identity, ApplicationUserManager userManager)
        {
            try
            {
                var user = await userManager.FindByNameAsync(identity.Name);
                if (user == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "User info not found"
                };

                var item = await _context.Users.Where(x => x.Id == user.Id).Select(t => new
                {
                    t.FirstName,
                    t.LastName,
                    t.PhoneNumber,
                    t.ImagePath,
                    Gender = t.Gender.ToString()
                }).FirstOrDefaultAsync(); ;
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "User not found"
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Record = item }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
        public async Task<APIResponse> CreateRole(string name, string normalizeName, bool restricted, ApplicationRoleManager roleManager)
        {
            try
            {
                var result = await roleManager.CreateAsync(new ApplicationRole { Name = name, NormalizeName = normalizeName, Restricted = restricted });
                if (!result.Succeeded) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(", ", result.Errors)
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Created"
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> RequestForOtp(string pin)
        {
            string errorMsg = string.Empty;
            try
            {
                //only for parmenant employee
                // var trainee = await _context.Trainees.Where(x => x.PIN == pin && x.TraineeType == TraineeType.Permanent).Select(x => new { x.UserId, x.PhoneNo }).FirstOrDefaultAsync(); 

                //all emp type
                var trainee = await _context.Trainees.Where(x => x.PIN == pin).Select(x => new { x.UserId, x.PhoneNo }).FirstOrDefaultAsync();
                if (trainee == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "No user found with this PIN"
                };

                if (string.IsNullOrEmpty(trainee.PhoneNo)) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Mobile number not found for this trainee"
                };

                var phoneNo = Validator.ValidateMobileNo(trainee.PhoneNo);
                if (phoneNo.Length != 14) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Invalid mobile no: " + trainee.PhoneNo
                };

                var userOtp = await _context.UserOtps.FirstOrDefaultAsync(x => x.UserId == trainee.UserId) ?? new UserOtp { UserId = trainee.UserId };
                userOtp.Otp = Utility.RandomNumber(1000, 999999).ToString().PadLeft(6, '0');
                //userOtp.Otp = "123456"; // uncomment on UAT 
                userOtp.GeneratedTime = DateTime.UtcNow.ToKindLocal();
                userOtp.ExpiredTime = userOtp.GeneratedTime.AddMinutes(5);

                if (userOtp.Id == Guid.Empty)
                {
                    userOtp.Id = Guid.NewGuid();
                    _context.UserOtps.Add(userOtp);
                }
                else
                {
                    _context.Entry(userOtp).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();

                try
                {
                    LogControl.Write("Trying to send otp");
                    await new SMSSender().SendAsync(phoneNo, $"Your OTP for BBL ALO reset password is {userOtp.Otp}. This OTP will expire within 5 minutes. N.B. new password will be applicable to your BBL ALO, BBL HR Apps.", Common.SMS.Enums.SMSEventType.PasswordResetOTP);
                }
                catch (Exception ex)
                {
                    LogControl.Write("SMSSender Exception :" + ex.Message);
                    errorMsg = ex.Message;
                }

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = trainee.PhoneNo,
                    Message = errorMsg
                };
            }
            catch (Exception ex)
            {
                LogControl.Write("ResetPasswordByOtp Message catch :" + ex.Message);
                LogControl.Write("ResetPasswordByOtp stac trace :" + ex.StackTrace);
                LogControl.Write("ResetPasswordByOtp InnerException" + ex.InnerException.ToString());
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
        public async Task<APIResponse> RequestForOtpApp(string pin)
        {
            string errorMsg = string.Empty;
            try
            {
                var trainee = await _context.Trainees.Where(x => x.PIN == pin).Select(x => new { x.UserId, x.PhoneNo }).FirstOrDefaultAsync();
                if (trainee == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "No user found with this PIN"
                };

                if (string.IsNullOrEmpty(trainee.PhoneNo)) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Mobile number not found for this trainee"
                };

                var phoneNo = Validator.ValidateMobileNo(trainee.PhoneNo);
                if (phoneNo.Length != 14) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Invalid mobile no: " + trainee.PhoneNo
                };

                var userOtp = await _context.UserOtps.FirstOrDefaultAsync(x => x.UserId == trainee.UserId);
                if (userOtp == null) userOtp = new UserOtp { UserId = trainee.UserId };

                userOtp.Otp = Utility.RandomNumber(1000, 999999).ToString().PadLeft(6, '0');
                //userOtp.Otp = "123456"; // uncomment on UAT 
                userOtp.GeneratedTime = DateTime.UtcNow.ToKindLocal();
                userOtp.ExpiredTime = userOtp.GeneratedTime.AddMinutes(5);

                if (userOtp.Id == Guid.Empty)
                {
                    userOtp.Id = Guid.NewGuid();
                    _context.UserOtps.Add(userOtp);
                }
                else
                {
                    _context.Entry(userOtp).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();

                try
                {
                    await new SMSSender().SendAsync(phoneNo, $"Your OTP for BBL ALO reset password is {userOtp.Otp}. This OTP will expire within 5 minutes. N.B. new password will be applicable to your BBL ALO, BBL HR Apps.", Common.SMS.Enums.SMSEventType.PasswordResetOTP);
                }
                catch (Exception ex)
                {
                    LogControl.Write("SMSSender Exception :" + ex.Message);
                    errorMsg = ex.Message;
                }

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { trainee.PhoneNo, OTP = userOtp.Otp },
                    Message = "This OTP will expire within 5 minutes. "
                };
            }
            catch (Exception ex)
            {
                LogControl.Write("ResetPasswordByOtp Message catch :" + ex.Message);
                LogControl.Write("ResetPasswordByOtp stac trace :" + ex.StackTrace);
                LogControl.Write("ResetPasswordByOtp InnerException" + ex.InnerException.ToString());
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> ValidateOtp(string pin, string otp)
        {
            try
            {
                var user = await _context.Trainees.FirstOrDefaultAsync(x => x.PIN == pin);
                if (user == null)
                {
                    return new APIResponse
                    {
                        Status = ResponseStatus.Error,
                        Message = "Invalid user"
                    };
                }
                var userOtp = await _context.UserOtps.Include(x => x.User).FirstOrDefaultAsync(x => (x.User.Id == user.UserId || x.User.UserName == pin) && x.Otp == otp);
                if (userOtp == null || DateTime.UtcNow.ToKindLocal() > userOtp.ExpiredTime)
                {
                    return new APIResponse
                    {
                        Status = ResponseStatus.Error,
                        Message = "The OTP is invalid or expired. Please re-enter your PIN to request a new OTP."
                    };
                }
                
                
                
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new 
                    { 
                        ExpireTime = DateTime.UtcNow.ToKindLocal() <= userOtp.ExpiredTime,
                        userOtp.Otp 
                    }
                };
            }
            catch (Exception ex)
            {
                LogControl.Write("ResetPasswordByOtp Message catch :" + ex.Message);
                LogControl.Write("ResetPasswordByOtp stac trace :" + ex.StackTrace);
                LogControl.Write("ResetPasswordByOtp InnerException" + ex.InnerException.ToString());
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> ResetPasswordByOtp(PasswordResetModel model, ApplicationUserManager userManager)
        {
            try
            {
                if (string.IsNullOrEmpty(model.Pin))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Pin is null"
                    };

                //var query = _context.UserOtps.Include(x => x.User);
                var traineeUser = await _context.Trainees.Where(x => x.PIN == model.Pin).FirstOrDefaultAsync();
                var userOtp = await _context.UserOtps.Include(x => x.User).FirstOrDefaultAsync(x => (x.User.Id == traineeUser.UserId || x.User.UserName == model.Pin) && x.Otp == model.Otp);
                //var userOtp = await query.FirstOrDefaultAsync(x => x.User.UserName == model.Pin);
                //if (userOtp == null)
                //{
                    
                //    userOtp = await query.FirstOrDefaultAsync(x => x.UserId == userid && x.Otp.Equals(model.Otp));
                //}

                bool notGuest = userOtp.User.UserType != UserType.Guest;
                string passwordHash = "";
                var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == model.Pin || x.Id == traineeUser.UserId);
                var validatePassword = userManager.PasswordValidator.ValidateAsync(model.Password);
                if(!validatePassword.Result.Succeeded)
                {
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = @"Error on user creation: - " + string.Join(" - ", validatePassword.Result.Errors.FirstOrDefault())
                    };
                }

                passwordHash = Cipher.EncryptSHA256Str(model.Password);
                if (notGuest)
                {
                    await UpdatePasswordForHRMUser(model.Pin, passwordHash);
                }
                user.LastPasswordChanged = DateTime.Now.ToKindLocal();
                //update for all UpdatePasswordForELearningUser
                await UpdatePasswordForELearningUser(userOtp.UserId, passwordHash);

                if (userOtp != null)
                {
                    //await DeleteCurrentOtpAfterPasswordReset(userOtp.UserId, userOtp.Otp);
                    _context.Entry(userOtp).State = EntityState.Deleted;
                }
                await _context.SaveChangesAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Password Successfully Reset"
                };
            }
            catch (Exception ex)
            {
                LogControl.Write("ResetPasswordByOtp Message catch :" + ex.Message);
                LogControl.Write("ResetPasswordByOtp stac trace :" + ex.StackTrace);
                LogControl.Write("ResetPasswordByOtp InnerException" + ex.InnerException.ToString());
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task DeleteCurrentOtpAfterPasswordReset(string userId, string otp)
        {
            System.Data.SqlClient.SqlCommand command = null;

            try
            {
                var con = new System.Data.SqlClient.SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
                command = new System.Data.SqlClient.SqlCommand("Delete * from UserOtp where UserId=@userid and Otp=@otp", con);

                command.Parameters.Add("@userId", System.Data.SqlDbType.NVarChar);
                command.Parameters.Add("@Otp", System.Data.SqlDbType.NVarChar);
                command.Parameters["@userId"].Value = userId;

                command.Parameters["@Otp"].Value = otp;

                LogControl.Write(" attempted to open connection to otp delete......");
                command.Connection.Open();
                LogControl.Write("connection opened.");
                int executedRows = await command.ExecuteNonQueryAsync();
                LogControl.Write("Otp deleted Success ");
                if (executedRows == 0) throw new Exception("Otp delete failed");
            }
            catch (Exception ex)
            {
                LogControl.Write("Message catch :" + ex.Message);
                LogControl.Write("stac trace :" + ex.StackTrace);
                LogControl.Write("InnerException" + ex.InnerException.ToString());
                throw (ex);
            }
            finally
            {
                LogControl.Write(" attempted to open connection close......");
                if (command.Connection != null && command.Connection.State != System.Data.ConnectionState.Closed)
                {
                    command.Connection.Close();
                }
                LogControl.Write("connection closed......");

            }
        }
        private async Task UpdatePasswordForHRMUser(string username, string password)
        {
            System.Data.SqlClient.SqlCommand command = null;

            try
            {
                //LogControl.Write("BBLHrmConnection" + System.Configuration.ConfigurationManager.ConnectionStrings["BBLHrmConnection"].ConnectionString);
                var con = new System.Data.SqlClient.SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["BBLHrmConnection"].ConnectionString);
                //LogControl.Write("UserId"+con.Credential.UserId);
                //LogControl.Write("Password"+con.Credential.Password.ToString());
                command = new System.Data.SqlClient.SqlCommand("UPDATE Hr_Mob_UsersInfo SET Password=@password WHERE UserId=@username", con);
                //LogControl.Write("CommandText" + command.CommandText);

                command.Parameters.Add("@username", System.Data.SqlDbType.NVarChar);
                command.Parameters.Add("@password", System.Data.SqlDbType.NVarChar);
                command.Parameters["@username"].Value = username;

                //command.Parameters["@password"].Value = Cipher.EncryptSHA256Str(password);
                command.Parameters["@password"].Value = password;

                LogControl.Write(" attempted to open connection......");
                command.Connection.Open();
                LogControl.Write("connection opened.");
                int executedRows = await command.ExecuteNonQueryAsync();
                LogControl.Write("Query execution Success ");
                if (executedRows == 0) throw new Exception("Password update failed");
            }
            catch (Exception ex)
            {
                LogControl.Write("Message catch :" + ex.Message);
                LogControl.Write("stac trace :" + ex.StackTrace);
                LogControl.Write("InnerException" + ex.InnerException.ToString());
                throw (ex);
            }
            finally
            {
                LogControl.Write(" attempted to open connection close......");
                if (command.Connection != null && command.Connection.State != System.Data.ConnectionState.Closed)
                {
                    command.Connection.Close();
                }
                LogControl.Write("connection closed......");

            }
        }
        private async Task UpdatePasswordForELearningUser(string userId, string password)
        {
            System.Data.SqlClient.SqlCommand command = null;

            try
            {

                var con = new System.Data.SqlClient.SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
                command = new System.Data.SqlClient.SqlCommand("UPDATE IdentityUser SET PasswordHash=@password WHERE Id=@username", con);

                command.Parameters.Add("@username", System.Data.SqlDbType.NVarChar);
                command.Parameters.Add("@password", System.Data.SqlDbType.NVarChar);
                command.Parameters["@username"].Value = userId;

                command.Parameters["@password"].Value = password;

                LogControl.Write(" attempted to open elearning connection......");
                command.Connection.Open();
                LogControl.Write("elearning connection opened.");
                int executedRows = await command.ExecuteNonQueryAsync();
                LogControl.Write("elearning Query execution Success ");
                if (executedRows == 0) throw new Exception("Password update failed");
            }
            catch (Exception ex)
            {
                LogControl.Write("elearning Message catch :" + ex.Message);
                LogControl.Write("elearning stac trace :" + ex.StackTrace);
                LogControl.Write("elearning InnerException" + ex.InnerException.ToString());
                throw (ex);
            }
            finally
            {
                LogControl.Write(" attempted to close elearning connection......");
                if (command.Connection != null && command.Connection.State != System.Data.ConnectionState.Closed)
                {
                    command.Connection.Close();
                }
                LogControl.Write("elearning connection closed......");

            }
        }

        public async Task<APIResponse> CreateOrUpdateGuestUser(GuestAccountModel model, ApplicationUserManager userManager)
        {
            bool isEdit = true;
            try
            {
                ApplicationUser user;
                if (!string.IsNullOrEmpty(model.Id))
                {
                    user = await userManager.FindByIdAsync(model.Id);
                    if (user == null)
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "User not found"
                        };
                }
                else
                {
                    user = new ApplicationUser { UserName = model.Email, Email = model.Email, UserType = UserType.Guest, LoginType = LoginType.Regular };
                    isEdit = false;
                }

                user.FirstName = model.FirstName;
                user.LastName = model.LastName;
                user.PhoneNumber = model.PhoneNo;
                if (user.Active && !model.IsActive) user.DeActivateOn = DateTime.UtcNow;
                user.Active = model.IsActive;

                userManager.MaxFailedAccessAttemptsBeforeLockout = 3;
                userManager.UserLockoutEnabledByDefault = true;
                userManager.DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(5);

                userManager.PasswordValidator = new PasswordValidator
                {
                    RequiredLength = 6,
                    RequireNonLetterOrDigit = false,
                    RequireDigit = false,
                    RequireLowercase = false,
                    RequireUppercase = false
                };

                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    try
                    {
                        IdentityResult result;
                        if (isEdit)
                        {
                            result = await userManager.UpdateAsync(user);
                            if (!result.Succeeded)
                            {
                                return new APIResponse
                                {
                                    Status = ResponseStatus.Warning,
                                    Message = string.Join(", ", result.Errors)
                                };
                            }
                        }
                        else
                        {
                            result = await userManager.CreateAsync(user, model.PhoneNo);
                            if (!result.Succeeded)
                            {
                                return new APIResponse
                                {
                                    Status = ResponseStatus.Warning,
                                    Message = string.Join(", ", result.Errors)
                                };
                            }

                            result = await userManager.AddToRoleAsync(user.Id, "Guest");
                            if (!result.Succeeded)
                            {
                                return new APIResponse
                                {
                                    Status = ResponseStatus.Warning,
                                    Message = string.Join(", ", result.Errors)
                                };
                            }
                        }
                        scope.Complete();
                    }
                    catch (Exception ex)
                    {
                        scope.Dispose();
                        return new APIResponse
                        {
                            Status = ResponseStatus.Error,
                            Message = ex.Message
                        };
                    }
                }

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> GetGuestUserList(string text, int size, int pageNumber)
        {
            try
            {
                var query = _context.Users.Where(x => x.UserType == UserType.Guest);

                if (!string.IsNullOrEmpty(text)) query = query.Where(x => x.UserName.Contains(text) || x.FirstName.Contains(text) || x.LastName.Contains(text));

                var data = await query
                    .Select(x => new { x.Id, x.UserName, x.FirstName, x.LastName, x.Active, x.LastLogOn, x.PhoneNumber })
                    .OrderBy(x => x.FirstName).ThenBy(x => x.LastName)
                  .Skip(pageNumber * size).Take(size).ToListAsync();

                var count = await query.CountAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }
    }

    public interface IAccountService
    {
        Task<APIResponse> GetUserList(string text, int size, int pageNumber, IIdentity identity);
        Task<APIResponse> GetUserById(string id, ApplicationUserManager userManager);
        Task<APIResponse> GetAdminUserById(string id, ApplicationUserManager userManager);
        Task<APIResponse> CreateOrUpdateUser(AccountModel model, ApplicationUserManager userManager);
        Task<APIResponse> CreateOrUpdateAdminUser(AdminAccountModel model, ApplicationUserManager userManager);
        Task<APIResponse> RegisterUser(AccountModel model, ApplicationUserManager userManager);
        Task<APIResponse> ResetPassword(string id, ApplicationUserManager userManager);
        Task<APIResponse> ChangePassword(PasswordChangeModel model, ApplicationUserManager userManager, IIdentity identity);
        Task<APIResponse> ForgetPassword(string email, ApplicationUserManager userManager);
        Task<APIResponse> SetNewPassword(ForgetPasswordModel model, ApplicationUserManager userManager);
        Task<APIResponse> UpdateProfile(UserProfileModel model, IIdentity identity, ApplicationUserManager userManager);
        Task<APIResponse> UpdatePhoto(IIdentity identity, ApplicationUserManager userManager);
        Task<APIResponse> GetProfile(IIdentity identity, ApplicationUserManager userManager);
        Task<APIResponse> CreateRole(string name, string normalizeName, bool restricted, ApplicationRoleManager roleManager);
        Task<APIResponse> RequestForOtp(string pin);
        Task<APIResponse> RequestForOtpApp(string pin);
        Task<APIResponse> ValidateOtp(string pin, string otp);
        Task<APIResponse> ResetPasswordByOtp(PasswordResetModel model, ApplicationUserManager userManager);
        Task<APIResponse> CreateOrUpdateGuestUser(GuestAccountModel model, ApplicationUserManager userManager);
        Task<APIResponse> GetGuestUserList(string text, int size, int pageNumber);
    }
}
