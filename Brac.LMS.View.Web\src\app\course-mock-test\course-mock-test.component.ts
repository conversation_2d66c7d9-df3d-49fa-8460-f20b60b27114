import { Component, TemplateRef, OnInit, ViewChild } from '@angular/core';
import { Location } from '@angular/common';
import { FormBuilder } from '@angular/forms';
import { environment } from '../../environments/environment';
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';
import { CommonService } from '../_services/common.service';
import { BlockUI, NgBlockUI } from 'ng-block-ui';
import { Timer } from 'src/app/_models/timer';
import { ConfirmService } from 'src/app/_helpers/confirm-dialog/confirm.service';
import { NgxSmartModalService, NgxSmartModalComponent } from 'ngx-smart-modal';
import { ToastrService } from 'ngx-toastr';
import { ResponseStatus } from 'src/app/_models/enum';
import { Subscription } from 'rxjs';
import { LocalStorageHelper } from 'src/app/_helpers/local-storage-helper';
import { AuthenticationService } from '../_services/authentication.service';
import { trigger, transition, style, animate } from '@angular/animations';
import { Page } from '../_models/page';
import { PaginationService } from '../shared/pagination/pagination.service';

@Component({
  selector: 'app-course-mock-test',
  templateUrl: './course-mock-test.component.html',
  styleUrls: ['./course-mock-test.component.css'],
  animations: [
    trigger('inOutAnimation', [
      transition(':enter', [
        style({ height: 0, opacity: 0 }),
        animate('0.5s ease-out', style({ height: 50, opacity: 1 })),
      ]),
      transition(':leave', [
        style({ height: 50, opacity: 1 }),
        animate('0.5s ease-in', style({ height: 0, opacity: 0 })),
      ]),
    ]),
  ],
})
export class CourseMockTestComponent implements OnInit {
  baseUrl = environment.baseUrl;
  mediaBaseUrl = environment.mediaBaseUrl;

  private currentUser: any;
  private quiz: any;
  quizType: string;
  examId: string;
  definitions: any[];
  traineeMockTestId: string;
  pagedetail: any;
  quizRunning: boolean = false;
  questionList: Array<any> = [];
  qIndex: number = -1;
  btnNextText: string = 'Next';
  noOfAnsweredQs: number = 0;
  mcqList: Array<any> = [];
  timer: Timer = new Timer();
  result: any;
  resultList: Array<any> = [];
  resultModal: NgxSmartModalComponent;
  resultDetailModal: NgxSmartModalComponent;
  @ViewChild(TemplateRef, { static: false }) tpl: TemplateRef<any>;
  @BlockUI() blockUI: NgBlockUI;
  currentTime: Date;
  examTime: Date;
  allowAnswerSubmit: boolean = false;
  page = new Page();

  timerSubscription: Subscription;
  examTimeCheckerInterval: any;

  interval: any;

  isConnected = true;
  answerStatus = '';
  notAllowMessage: string;
  popStateHandler: any;
  constructor(
    private router: Router,
    private _service: CommonService,
    public ngxSmartModalService: NgxSmartModalService,
    private toastr: ToastrService,
    private confirmService: ConfirmService,
    private authService: AuthenticationService,
    private pageService: PaginationService,
    private route: ActivatedRoute,
    private _location: Location
  ) {
    // this.courseId = this.route.snapshot.paramMap.get("courseId");
    this.examId = this.route.snapshot.paramMap.get('examId');
    this.baseUrl = environment.baseUrl;

    this.authService.getCurrentUser().subscribe((user) => {
      this.currentUser = user;
    });
    this.page.startsFrom = 1;
    this.page.pageNumber = 1;
    this.page.size = 1;
    this.router.events.subscribe((evt) => {
      if (!(evt instanceof NavigationEnd)) {
        return;
      }
      window.scrollTo(0, 0);
    });
  }

  ngOnInit(): void {
    this.getCourseMockTestDetails();
  }
  ngAfterViewInit() {
    // window.addEventListener('popstate', (event) => {
    //   this.quizRunning ? alert("Your exam has been terminated!") : true;
    //   this.quizRunning ? this.saveAnswer(true) : true;
    // });
    this.popStateHandler = (event) => {
      if (this.quizRunning) {
        alert('Your exam has been terminated!');
        this.saveAnswer(true);
      }
    };
    window.addEventListener('popstate', this.popStateHandler);
  }
  ngOnDestroy() {
    if (this.popStateHandler) {
      window.removeEventListener('popstate', this.popStateHandler);
    }
    if (this.timerSubscription) this.timerSubscription.unsubscribe();
    if (this.interval) clearInterval(this.interval);
  }

  getHourMint(duration: number) {
    return Math.floor(duration / 60) + 'h ' + (duration % 60) + 'm';
  }

  changeTab(type, e) {
    switch (type) {
      case 'Results':
        if (this.resultList.length === 0) this.getExamResult();
        break;
      default:
        break;
    }
  }

  getCourseMockTestDetails() {
    this.blockUI.start('Getting data...');
    this._service.get('exam/mock-test/get-exam-info/' + this.examId).subscribe({
      next: (res: any) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
          this._location.back();
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, 'Error!', {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          this._location.back();
          return;
        }
        this.pagedetail = res.Data;
        console.log('this.pagedetail', this.pagedetail);
        this.notAllowMessage = res.Message;
        if (!res.Data.CanAttend) return;
      },
      error: (err) => {
        this.toastr.warning(err.Messaage || err, 'Error!', {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
        this.blockUI.stop();
      },
      complete: () => this.blockUI.stop(),
    });
  }
  selectSpecific(lists) {
    lists.map((x) => (x.Selected = false));
  }
  startQuiz() {
    this.questionList = [];
    const savedExam = LocalStorageHelper.get(
      this.examId + '~' + this.currentUser.Id
    );
    if (savedExam) {
      this.questionList = savedExam.QuestionList;
      this.quiz = {
        StartDate: savedExam.StartDate
          ? savedExam.StartDate
          : new Date().toISOString(),
      };

      this.qIndex = savedExam.QIndex;
      this.quizRunning = true;
      this.allowAnswerSubmit = true;

      this.timerSubscription = this.timer
        .start(savedExam.ExamTime)
        .subscribe((status) => {
          if (status === 'ended') {
            this.onTimesUp();
            this.timerSubscription.unsubscribe();
          }
        });
    } else {
      this.blockUI.start('Starting exam. Please wait...');
      this._service
        .get('exam/mock-test/get-questions/' + this.examId)
        .subscribe({
          next: (res: any) => {
            if (res.Status === ResponseStatus.Warning) {
              this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
              return;
            } else if (res.Status === ResponseStatus.Error) {
              this.toastr.error(res.Message, 'Error!', {
                closeButton: true,
                disableTimeOut: false,
                enableHtml: true,
              });
              return;
            }

            // **FIX: Use ISO string for proper datetime serialization**
            this.quiz = { StartDate: new Date().toISOString() };
            this.traineeMockTestId = res.Data.TraineeMockTestId;
            res.Data.MCQs.forEach((element) => {
              this.questionList.push({
                Id: element.Id,
                Question: element.Question,
                Options: [
                  { Text: element.Option1, Selected: false },
                  { Text: element.Option2, Selected: false },
                  { Text: element.Option3, Selected: false },
                  { Text: element.Option4, Selected: false },
                ],
                Mark: element.Mark,
              });
            });

            this.qIndex = 0;
            this.quizRunning = true;
            this.allowAnswerSubmit = true;

            this.timerSubscription = this.timer
              .start(this.pagedetail.DurationMnt * 60)
              .subscribe((status) => {
                if (status === 'ended') {
                  this.onTimesUp();
                  this.timerSubscription.unsubscribe();
                }
              });
          },
          error: (err) => {
            this.toastr.error(err.message || err, 'Error!', {
              closeButton: true,
              disableTimeOut: true,
              enableHtml: true,
            });
            this.blockUI.stop();
          },
          complete: () => this.blockUI.stop(),
        });
    }
  }
  onPageChange(page: number) {
    this.setPage(page);
  }
  setPage(pageNumber) {
    this.page.pageNumber = pageNumber;
    this.qIndex = pageNumber - 1;
    this.btnNextText =
      this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';
  }
  onTimesUp() {
    //  this.saveAnswerSheetIntoLocalStorage(true);
    this.ngxSmartModalService.closeAll();
    this.confirmService.close();
    this.saveAnswer(true);

    this.toastr.success(
      'Your answer will get submitted automatically',
      "Time's Up!!"
    );
  }

  nextQuestion() {
    if (this.qIndex < this.questionList.length - 1) this.qIndex++;
    else {
      this.timer.pause();
      this.submitAnswer();
      return;
    }
    this.btnNextText =
      this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';
    this.page.pageNumber = this.qIndex + 1;
    this.pageService.currentPage.next(this.page.pageNumber);
  }

  prevQuestion() {
    if (this.qIndex !== 0) this.qIndex--;
    this.page.pageNumber = this.page.pageNumber - 1;
    this.pageService.currentPage.next(this.page.pageNumber);
  }

  submitAnswer() {
    this.confirmService
      .confirm(
        'Are you sure?',
        'You are going to submit answer. ' + this.answerStatus,
        'Yes, Submit Answer'
      )
      .subscribe((result) => {
        if (result) {
          this.timer.pause();
          this.saveAnswer();
        } else {
          this.timer.resume();
        }
      });
  }

  private saveAnswer(autoSubmission: boolean = false) {
    // const data = LocalStorageHelper.get(this.examId + '~' + this.currentUser.Id);

    if (!this.isConnected) {
      this.toastr.error('You have no internet connection', 'ALERT!', {
        timeOut: 4000,
      });
      this.timer.resume();
      return;
    }

    let submittedMCQList = [];
    this.questionList.forEach((element) => {
      submittedMCQList.push({
        QuestionId: element.Id,
        Answered: element.Options.map(function (x, i) {
          if (x.Selected) return i + 1;
          else return 0;
        })
          .filter((x) => x > 0)
          .join(),
      });
    });

    const obj = {
      TraineeMockTestId: this.traineeMockTestId,
      ExamId: this.examId,
      // **FIX: Use ISO strings for proper datetime serialization**
      StartTime: this.quiz.StartDate,
      EndTime: new Date().toISOString(),
      AutoSubmission: autoSubmission,
      MCQList: submittedMCQList,
    };

    this.blockUI.start('Submitting answer...');
    this._service.post('exam/mock-test/save-answers', obj).subscribe({
      next: (res: any) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.error(res.Message, 'Warning!', {
            closeButton: true,
            disableTimeOut: true,
            enableHtml: true,
          });
          this.timer.resume();
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, 'Error!', {
            closeButton: true,
            disableTimeOut: true,
            enableHtml: true,
          });
          this.timer.resume();
          return;
        }

        this.confirmService.close();
        this.allowAnswerSubmit = false;
        this.result = res.Data;

        this.getExamResult();
        this.result.Questions.forEach((element) => {
          element.Options = [];
          for (let i = 1; i <= 4; i++) {
            let option = {
              id: i,
              value: element['Option' + i],
              answered: element.Answered.indexOf(i.toString()) !== -1,
              correctAns: element.CorrectAnswers.indexOf(i.toString()) !== -1,
              correct: false,
            };
            option.correct = option.answered && option.correctAns;
            element.Options.push(option);
          }
        });
        if (!this.resultModal)
          this.resultModal = this.ngxSmartModalService.create(
            'resultModal',
            this.tpl
          );
        this.resultModal.open();
      },
      error: (err) => {
        this.timer.resume();
        this.toastr.error(err.message || err, 'Error!', {
          closeButton: true,
          disableTimeOut: true,
          enableHtml: true,
        });
        this.blockUI.stop();
      },
      complete: () => {
        this.blockUI.stop();
      },
    });
  }

  getExamResult() {
    this._service
      .get('exam/mock-test/get-exam-results/' + this.examId)
      .subscribe({
        next: (res: any) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, 'Error!', { timeOut: 2000 });
            return;
          }
          this.resultList = res.Data;
        },
        error: (err) => {
          this.toastr.warning(err.Messaage || err, 'Error!', {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          this.blockUI.stop();
        },
        complete: () => console.log('complete'),
      });
  }

  // private saveAnswerSheetIntoLocalStorage(autoSubmission: boolean = false) {
  //    let submittedMCQList = [], submittedTFQList = [], submittedFIGQList = [], submittedLRMQList = [], submittedWQList = [];
  //   this.questionList.filter(x => x.Type === 'MCQ').forEach(element => {
  //     submittedMCQList.push({
  //       QuestionId: element.Id,
  //       Answered: element.Options.map(function (x, i) {
  //         if (x.Selected) return i + 1;
  //         else return 0;
  //       }).filter(x => x > 0).join()
  //     });
  //   });
  //   // this.questionList.filter(x => x.Type === 'TFQ').forEach(element => {
  //   //   submittedTFQList.push({
  //   //     QuestionId: element.Id,
  //   //     Answered: element.Answer,
  //   //     CorrectAnswer: !element.Answer ? (element.CorrectAnswer ? element.CorrectAnswer.trim() : element.CorrectAnswer) : null
  //   //   });
  //   // });
  //   // this.questionList.filter(x => x.Type === 'FIGQ').forEach(element => {
  //   //   submittedFIGQList.push({
  //   //     QuestionId: element.Id,
  //   //     Answered: element.Answer ? element.Answer.trim() : element.Answer
  //   //   });
  //   // });

  //   // let mQuestions = this.questionList.find(x => x.Type === 'LRMQ');
  //   // if (mQuestions)
  //   //   for (let i = 0; i < mQuestions.LeftSides.length; i++) {
  //   //     const element = mQuestions.LeftSides[i];
  //   //     submittedLRMQList.push({
  //   //       QuestionId: element.Id,
  //   //       Answered: mQuestions.RightSides[i]
  //   //     });
  //   //   }

  //   // this.questionList.filter(x => x.Type === 'WQ').forEach(element => {
  //   //   submittedWQList.push({
  //   //     QuestionId: element.Id,
  //   //     Answered: element.Answer ? element.Answer.trim() : element.Answer
  //   //   });
  //   // });

  //   const obj = {
  //     ExamId: this.examId,
  //     StartTime: this.quiz.StartDate,
  //     EndTime: new Date().toISOString(),
  //     AutoSubmission: autoSubmission,
  //     ExamTime: this.timer.timeLeft,
  //     QIndex: this.qIndex,
  //    // MCQList: submittedMCQList,
  //     // TFQList: submittedTFQList,
  //     // FIGQList: submittedFIGQList,
  //     // LRMQList: submittedLRMQList,
  //     // WQList: submittedWQList,
  //     QuestionList: this.questionList
  //   }
  //   LocalStorageHelper.setWithExpiry(this.examId + '~' + this.currentUser.Id, obj, 3);

  //   // this.answerStatus = '<br/>';
  //   // if (obj.MCQList.length > 0) this.answerStatus += 'MCQ answered: ' + obj.MCQList.filter(x => x.Answered).length + " out of " + obj.MCQList.length;

  //   // if (obj.TFQList.length > 0) this.answerStatus += ' <br/> True/False answered - True: ' + obj.TFQList.filter(x => x.Answered).length + " & False: " + obj.TFQList.filter(x => !x.Answered).length;

  //   // if (obj.FIGQList.length > 0) this.answerStatus += ' <br/> Fill in the gap answered: ' + obj.FIGQList.filter(x => x.Answered).length + " out of " + obj.FIGQList.length;

  //   // if (obj.WQList.length > 0) this.answerStatus += ' <br/> Written answered ' + obj.WQList.filter(x => x.Answered).length + " out of " + obj.WQList.length;
  // }

  resultModalClose() {
    this.quizRunning = false;
    this.timer.stop();
    this.timerSubscription.unsubscribe();
    this._location.back();
    this.result = null;
  }

  backClicked() {
    this.quizRunning ? this.saveAnswer(true) : true;
    !this.quizRunning ? this._location.back() : true;
  }
}
