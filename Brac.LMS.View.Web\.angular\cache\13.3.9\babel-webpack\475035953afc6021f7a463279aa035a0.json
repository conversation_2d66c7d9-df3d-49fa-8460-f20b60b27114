{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { TemplateRef } from '@angular/core';\nimport { environment } from '../../environments/environment';\nimport { BlockUI } from 'ng-block-ui';\nimport { Timer } from 'src/app/_models/timer';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { moveItemInArray } from '@angular/cdk/drag-drop';\nimport { LocalStorageHelper } from 'src/app/_helpers/local-storage-helper';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { Page } from '../_models/page';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-smart-modal\";\nimport * as i4 from \"ngx-bootstrap/modal\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"src/app/_helpers/confirm-dialog/confirm.service\";\nimport * as i7 from \"../_services/authentication.service\";\nimport * as i8 from \"../shared/pagination/pagination.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"ng-block-ui\";\nimport * as i11 from \"@angular/flex-layout/extended\";\nimport * as i12 from \"../shared/pagination/pagination.component\";\nimport * as i13 from \"ngx-bootstrap/accordion\";\nimport * as i14 from \"@angular/forms\";\nimport * as i15 from \"ngx-toggle-switch\";\nimport * as i16 from \"@angular/cdk/drag-drop\";\nimport * as i17 from \"ngx-moment\";\nimport * as i18 from \"../_helpers/safe-pipe\";\nconst _c0 = [\"fullScreenElement\"];\nconst _c1 = [\"template\"];\n\nfunction CourseCertificateTestComponent_div_3_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelementStart(1, \"div\", 16);\n    i0.ɵɵelementStart(2, \"app-pagination\", 17);\n    i0.ɵɵlistener(\"pageChange\", function CourseCertificateTestComponent_div_3_div_14_Template_app_pagination_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return ctx_r5.onPageChange($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r3.questionList.length)(\"paginationControlToShow\", 10)(\"itemsPerPage\", ctx_r3.page.size)(\"definitions\", ctx_r3.questionList)(\"currentPage\", ctx_r3.page.pageNumber)(\"mqAnswered\", ctx_r3.mqAnswered);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_1_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelementStart(1, \"ul\", 25);\n    i0.ɵɵelementStart(2, \"li\");\n    i0.ɵɵelement(3, \"i\", 24);\n    i0.ɵɵtext(4, \" Open in : \");\n    i0.ɵɵelementStart(5, \"b\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"amDateFormat\");\n    i0.ɵɵpipe(8, \"amLocal\");\n    i0.ɵɵpipe(9, \"amFromUtc\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" to \");\n    i0.ɵɵelementStart(11, \"b\", 26);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"amDateFormat\");\n    i0.ɵɵpipe(14, \"amLocal\");\n    i0.ɵɵpipe(15, \"amFromUtc\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(7, 2, i0.ɵɵpipeBind1(8, 5, i0.ɵɵpipeBind1(9, 7, ctx_r10.pagedetail.StartDate)), \"MMM DD, YYYY hh:mm A\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(13, 9, i0.ɵɵpipeBind1(14, 12, i0.ɵɵpipeBind1(15, 14, ctx_r10.pagedetail.EndDate)), \"MMM DD, YYYY hh:mm A\"), \" \");\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_1_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelementStart(1, \"accordion\", 27);\n    i0.ɵɵelementStart(2, \"accordion-group\", 28);\n    i0.ɵɵelement(3, \"div\", 29);\n    i0.ɵɵpipe(4, \"safe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"isAnimated\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(4, 2, ctx_r11.pagedetail.ExamInstructions, \"html\"), i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelementStart(1, \"div\", 21);\n    i0.ɵɵelementStart(2, \"div\", 1);\n    i0.ɵɵelementStart(3, \"div\", 22);\n    i0.ɵɵelementStart(4, \"ul\", 23);\n    i0.ɵɵelementStart(5, \"li\");\n    i0.ɵɵelement(6, \"i\", 24);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"li\");\n    i0.ɵɵelement(9, \"i\", 24);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 22);\n    i0.ɵɵelementStart(12, \"ul\", 23);\n    i0.ɵɵelementStart(13, \"li\");\n    i0.ɵɵelement(14, \"i\", 24);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"li\");\n    i0.ɵɵelement(17, \"i\", 24);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, CourseCertificateTestComponent_div_3_div_16_div_1_div_19_Template, 16, 16, \"div\", 19);\n    i0.ɵɵtemplate(20, CourseCertificateTestComponent_div_3_div_16_div_1_div_20_Template, 5, 5, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" Total Marks : \", ctx_r7.pagedetail.Marks, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Question Type : \", ctx_r7.pagedetail.MCQOnly ? \"MCQ\" : \"Mixed\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Total Duration : \", ctx_r7.getHourMint(ctx_r7.pagedetail.DurationMnt), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Pending Quota : \", ctx_r7.pagedetail.PendingQuota, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.pagedetail.StartDate && ctx_r7.pagedetail.EndDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.pagedetail.ExamInstructions);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelementStart(1, \"h3\", 35);\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵtext(3, \" You have already obtained a certificate for this test \");\n    i0.ɵɵelementStart(4, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function CourseCertificateTestComponent_div_3_div_16_div_2_div_1_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(4);\n      return ctx_r15.downloadCertificate(ctx_r15.pagedetail.CourseId);\n    });\n    i0.ɵɵelement(5, \"i\", 38);\n    i0.ɵɵtext(6, \" Download Certificate \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelementStart(1, \"h3\", 35);\n    i0.ɵɵelement(2, \"i\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.notAllowMessage, \" \");\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_2_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function CourseCertificateTestComponent_div_3_div_16_div_2_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(4);\n\n      const _r1 = i0.ɵɵreference(5);\n\n      return ctx_r17.startQuiz(_r1);\n    });\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.pagedetail.CertificateAchieved ? \"Improvement\" : \"Start Exam\", \" \");\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, CourseCertificateTestComponent_div_3_div_16_div_2_div_1_Template, 7, 0, \"div\", 31);\n    i0.ɵɵtemplate(2, CourseCertificateTestComponent_div_3_div_16_div_2_div_2_Template, 4, 1, \"div\", 32);\n    i0.ɵɵtemplate(3, CourseCertificateTestComponent_div_3_div_16_div_2_button_3_Template, 3, 1, \"button\", 33);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.pagedetail.CertificateAchieved);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.pagedetail.Allow);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.pagedetail.Allow);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 59);\n    i0.ɵɵelementStart(3, \"p\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 61);\n    i0.ɵɵelementStart(6, \"p\", 60);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.questionList[ctx_r20.qIndex].Question, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" [\", ctx_r20.questionList[ctx_r20.qIndex].Mark, \"] \");\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_ul_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 64);\n    i0.ɵɵelementStart(1, \"input\", 65);\n    i0.ɵɵlistener(\"click\", function CourseCertificateTestComponent_div_3_div_16_div_3_div_14_ul_2_li_1_Template_input_click_1_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(6);\n      return ctx_r29.selectSpecific(ctx_r29.questionList[ctx_r29.qIndex].Options);\n    })(\"ngModelChange\", function CourseCertificateTestComponent_div_3_div_16_div_3_div_14_ul_2_li_1_Template_input_ngModelChange_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const option_r27 = restoredCtx.$implicit;\n      return option_r27.Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 66);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r27 = ctx.$implicit;\n    const oi_r28 = ctx.index;\n    const ctx_r26 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"name\", \"radioGroup\" + ctx_r26.qIndex)(\"ngModel\", option_r27.Selected)(\"id\", \"option\" + oi_r28 + \"-\" + ctx_r26.qIndex)(\"value\", option_r27.Text);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"option\" + oi_r28 + \"-\" + ctx_r26.qIndex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r27.Text, \" \");\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_ul_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 62);\n    i0.ɵɵtemplate(1, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_ul_2_li_1_Template, 4, 6, \"li\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r21.questionList[ctx_r21.qIndex].Options);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 59);\n    i0.ɵɵelementStart(3, \"p\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 61);\n    i0.ɵɵelementStart(6, \"p\", 60);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 67);\n    i0.ɵɵelementStart(9, \"div\", 1);\n    i0.ɵɵelementStart(10, \"div\", 6);\n    i0.ɵɵelementStart(11, \"ui-switch\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_3_Template_ui_switch_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext(5);\n      return ctx_r32.questionList[ctx_r32.qIndex].Answer = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.questionList[ctx_r22.qIndex].Question, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" [\", ctx_r22.questionList[ctx_r22.qIndex].Mark, \"] \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r22.questionList[ctx_r22.qIndex].Answer);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 59);\n    i0.ɵɵelementStart(3, \"p\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 61);\n    i0.ɵɵelementStart(6, \"p\", 60);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 67);\n    i0.ɵɵelementStart(9, \"div\", 69);\n    i0.ɵɵelementStart(10, \"span\", 70);\n    i0.ɵɵtext(11, \"Answer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 71);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_4_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(5);\n      return ctx_r34.questionList[ctx_r34.qIndex].Answer = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.questionList[ctx_r23.qIndex].Question, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" [\", ctx_r23.questionList[ctx_r23.qIndex].Mark, \"] \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r23.questionList[ctx_r23.qIndex].Answer);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_5_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelementStart(1, \"div\", 80);\n    i0.ɵɵelementStart(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 81);\n    i0.ɵɵelementStart(5, \"b\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r38 = ctx.$implicit;\n    const i_r39 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i_r39 + 1, \". \", item_r38.LeftSide, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"[\", item_r38.Mark, \"]\");\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_5_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 84);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_5_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵtemplate(1, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_5_div_13_div_1_Template, 1, 0, \"div\", 83);\n    i0.ɵɵelementStart(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r40 = ctx.$implicit;\n    const i_r41 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i_r41 + 1, \". \", item_r40, \"\");\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 6);\n    i0.ɵɵelementStart(3, \"div\", 72);\n    i0.ɵɵelementStart(4, \"div\", 44);\n    i0.ɵɵelementStart(5, \"h4\", 73);\n    i0.ɵɵtext(6, \"Matching Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 74);\n    i0.ɵɵelementStart(8, \"div\", 1);\n    i0.ɵɵelementStart(9, \"div\", 75);\n    i0.ɵɵtemplate(10, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_5_div_10_Template, 7, 3, \"div\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 77);\n    i0.ɵɵelementStart(12, \"div\", 78);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_5_Template_div_cdkDropListDropped_12_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext(5);\n      return ctx_r43.drop($event);\n    });\n    i0.ɵɵtemplate(13, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_5_div_13_Template, 4, 2, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r24.questionList[ctx_r24.qIndex].LeftSides);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r24.questionList[ctx_r24.qIndex].RightSides);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 59);\n    i0.ɵɵelementStart(3, \"p\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 61);\n    i0.ɵɵelementStart(6, \"p\", 60);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 67);\n    i0.ɵɵelementStart(9, \"div\", 69);\n    i0.ɵɵelementStart(10, \"span\", 70);\n    i0.ɵɵtext(11, \"Answer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"textarea\", 85);\n    i0.ɵɵlistener(\"ngModelChange\", function CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_6_Template_textarea_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(5);\n      return ctx_r45.questionList[ctx_r45.qIndex].Answer = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.questionList[ctx_r25.qIndex].Question, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" [\", ctx_r25.questionList[ctx_r25.qIndex].Mark, \"] \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r25.questionList[ctx_r25.qIndex].Answer);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_1_Template, 8, 3, \"div\", 56);\n    i0.ɵɵtemplate(2, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_ul_2_Template, 2, 2, \"ul\", 57);\n    i0.ɵɵtemplate(3, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_3_Template, 12, 4, \"div\", 56);\n    i0.ɵɵtemplate(4, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_4_Template, 13, 4, \"div\", 56);\n    i0.ɵɵtemplate(5, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_5_Template, 14, 3, \"div\", 56);\n    i0.ɵɵtemplate(6, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_div_6_Template, 13, 4, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.questionList[ctx_r19.qIndex].Type === \"MCQ\" && ctx_r19.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.questionList[ctx_r19.qIndex].Type === \"MCQ\" && ctx_r19.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.questionList[ctx_r19.qIndex].Type === \"TFQ\" && ctx_r19.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.questionList[ctx_r19.qIndex].Type === \"FIGQ\" && ctx_r19.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.questionList[ctx_r19.qIndex].Type === \"LRMQ\" && ctx_r19.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.questionList[ctx_r19.qIndex].Type === \"WQ\" && ctx_r19.timer.isRunning);\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelementStart(1, \"div\", 43);\n    i0.ɵɵelementStart(2, \"div\", 44);\n    i0.ɵɵelementStart(3, \"div\", 1);\n    i0.ɵɵelementStart(4, \"div\", 45);\n    i0.ɵɵelement(5, \"i\", 46);\n    i0.ɵɵelementStart(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 47);\n    i0.ɵɵelement(9, \"i\", 48);\n    i0.ɵɵelementStart(10, \"strong\");\n    i0.ɵɵtext(11, \" Time Left: \");\n    i0.ɵɵelementStart(12, \"b\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CourseCertificateTestComponent_div_3_div_16_div_3_div_14_Template, 7, 6, \"div\", 49);\n    i0.ɵɵelementStart(15, \"div\", 50);\n    i0.ɵɵelementStart(16, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function CourseCertificateTestComponent_div_3_div_16_div_3_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext(3);\n      return ctx_r47.prevQuestion();\n    });\n    i0.ɵɵelement(17, \"i\", 52);\n    i0.ɵɵtext(18, \" Prev \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function CourseCertificateTestComponent_div_3_div_16_div_3_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r49 = i0.ɵɵnextContext(3);\n\n      const _r1 = i0.ɵɵreference(5);\n\n      return ctx_r49.nextQuestion(_r1);\n    });\n    i0.ɵɵtext(20);\n    i0.ɵɵelement(21, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" QUESTION \", ctx_r9.qIndex + 1, \" of \", ctx_r9.questionList.length, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r9.timer.hour, \"h : \", ctx_r9.timer.minute, \"m : \", ctx_r9.timer.second, \"s\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.timer.isRunning);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.qIndex === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.btnNextText, \" \");\n  }\n}\n\nfunction CourseCertificateTestComponent_div_3_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, CourseCertificateTestComponent_div_3_div_16_div_1_Template, 21, 6, \"div\", 19);\n    i0.ɵɵtemplate(2, CourseCertificateTestComponent_div_3_div_16_div_2_Template, 4, 3, \"div\", 20);\n    i0.ɵɵtemplate(3, CourseCertificateTestComponent_div_3_div_16_div_3_Template, 22, 8, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.quizRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.quizRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.quizRunning);\n  }\n}\n\nconst _c2 = function (a0) {\n  return {\n    \"col-lg-12\": a0\n  };\n};\n\nfunction CourseCertificateTestComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵelementStart(2, \"div\", 6);\n    i0.ɵɵelementStart(3, \"div\", 7);\n    i0.ɵɵelementStart(4, \"div\", 8);\n    i0.ɵɵelementStart(5, \"p\", 9);\n    i0.ɵɵtext(6);\n    i0.ɵɵelement(7, \"br\");\n    i0.ɵɵelementStart(8, \"span\", 10);\n    i0.ɵɵtext(9, \"Certification Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"a\", 11);\n    i0.ɵɵlistener(\"click\", function CourseCertificateTestComponent_div_3_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return ctx_r50.examStarted ? ctx_r50.backClicked(true) : ctx_r50.backClicked(false);\n    });\n    i0.ɵɵelement(11, \"i\", 12);\n    i0.ɵɵtext(12, \"Go Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"hr\");\n    i0.ɵɵtemplate(14, CourseCertificateTestComponent_div_3_div_14_Template, 3, 6, \"div\", 13);\n    i0.ɵɵelementStart(15, \"div\", 14);\n    i0.ɵɵtemplate(16, CourseCertificateTestComponent_div_3_div_16_Template, 4, 3, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.quizRunning));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.pagedetail.CourseTitle, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.quizRunning);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.pagedetail);\n  }\n}\n\nfunction CourseCertificateTestComponent_ng_template_4_div_8_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 96);\n  }\n}\n\nfunction CourseCertificateTestComponent_ng_template_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵtemplate(1, CourseCertificateTestComponent_ng_template_4_div_8_img_1_Template, 1, 0, \"img\", 95);\n    i0.ɵɵelementStart(2, \"h5\");\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \" Your achieved mark is \");\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \" Your score is \");\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r52.result.Result === \"Passed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMapInterpolate1(\"cc-style-2 \", ctx_r52.result.Result === \"Passed\" ? \"text-overImage-centered\" : \"\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r52.result.CorrectAnswer, \"/\", ctx_r52.result.TotalQuestion, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r52.result.Result === \"Passed\" ? \"text-primary\" : \"text-danger\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r52.result.Score, \" %\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r52.result.Result === \"Passed\" ? \"You have completed the test successfully\" : \"You could not achieve the pass mark\", \" \");\n  }\n}\n\nfunction CourseCertificateTestComponent_ng_template_4_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function CourseCertificateTestComponent_ng_template_4_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return ctx_r56.tryAgain();\n    });\n    i0.ɵɵtext(1, \"Please try again\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseCertificateTestComponent_ng_template_4_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function CourseCertificateTestComponent_ng_template_4_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return ctx_r58.downloadCertificate(ctx_r58.result.CourseExamData.CourseId);\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \" Download \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CourseCertificateTestComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelementStart(1, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function CourseCertificateTestComponent_ng_template_4_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return ctx_r60.modalHide();\n    });\n    i0.ɵɵelementStart(2, \"span\", 88);\n    i0.ɵɵtext(3, \"\\u00D7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 89);\n    i0.ɵɵelementStart(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 90);\n    i0.ɵɵtemplate(8, CourseCertificateTestComponent_ng_template_4_div_8_Template, 13, 11, \"div\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 92);\n    i0.ɵɵtext(10);\n    i0.ɵɵtemplate(11, CourseCertificateTestComponent_ng_template_4_button_11_Template, 2, 0, \"button\", 93);\n    i0.ɵɵtemplate(12, CourseCertificateTestComponent_ng_template_4_button_12_Template, 3, 0, \"button\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMapInterpolate1(\"modal-title cc-style-2 \", ctx_r2.result.Result === \"Passed\" ? \"text-primary\" : \"text-danger\", \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.result.Result === \"Passed\" ? \"Congratulations!\" : \"Sorry!\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.result);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.result.Result === \"Passed\" ? \"Please download your certificate from Certificate Section\" : \"\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.result.Result !== \"Passed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.result.Result === \"Passed\");\n  }\n}\n\nexport class CourseCertificateTestComponent {\n  constructor(router, document, _service, ngxSmartModalService, modalService, toastr, confirmService, authService, route, pageService, _location) {\n    this.router = router;\n    this.document = document;\n    this._service = _service;\n    this.ngxSmartModalService = ngxSmartModalService;\n    this.modalService = modalService;\n    this.toastr = toastr;\n    this.confirmService = confirmService;\n    this.authService = authService;\n    this.route = route;\n    this.pageService = pageService;\n    this._location = _location;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.page = new Page();\n    this.quizRunning = false;\n    this.questionList = [];\n    this.qIndex = -1;\n    this.btnNextText = 'Next';\n    this.noOfAnsweredQs = 0;\n    this.mcqList = [];\n    this.timer = new Timer();\n    this.allowAnswerSubmit = false;\n    this.changeScreenCounter = 0;\n    this.terminated = null;\n    this.allowTabChange = false;\n    this.isConnected = true;\n    this.answerStatus = '';\n    this.examStarted = false;\n    this.modalConfig = {\n      class: 'gray',\n      backdrop: 'static'\n    };\n    this.mqAnswered = false;\n    this.answerSubmitted = false;\n    this.examId = this.route.snapshot.paramMap.get('examId');\n    this.baseUrl = environment.baseUrl;\n    this.window = this.document.defaultView;\n    this.authService.getCurrentUser().subscribe(user => {\n      this.currentUser = user;\n    });\n    this.page.startsFrom = 1;\n    this.page.pageNumber = 1;\n    this.page.size = 1;\n  }\n\n  ngOnInit() {\n    this.answerSubmitted = false;\n    this.getCourseCertificateTestDetails();\n  }\n\n  ngAfterViewInit() {\n    this.popStateHandler = event => {\n      if (this.examStarted) {\n        alert('Your exam has been terminated!');\n        this.saveAnswer(this.template);\n      }\n    };\n\n    window.addEventListener('popstate', this.popStateHandler);\n  }\n\n  ngOnDestroy() {\n    if (this.popStateHandler) {\n      window.removeEventListener('popstate', this.popStateHandler);\n    }\n\n    if (this.timerSubscription) this.timerSubscription.unsubscribe();\n    if (this.interval) clearInterval(this.interval);\n  }\n\n  getHourMint(duration) {\n    return Math.floor(duration / 60) + 'h ' + duration % 60 + 'm';\n  }\n\n  selectSpecific(lists) {\n    lists.map(x => x.Selected = false);\n  }\n\n  getCourseCertificateTestDetails() {\n    this.blockUI.start('Getting data...');\n\n    this._service.get('exam/certificate-test/get-exam-info/' + this.examId).subscribe({\n      next: res => {\n        console.trace(res);\n\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n\n          this._location.back();\n\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n\n          this._location.back();\n\n          return;\n        }\n\n        this.pagedetail = res.Data;\n        this.notAllowMessage = res.Message;\n        console.log('this.pagedetail ', this.pagedetail); //   this.quizType = res.Data.ExamType === 'CertificateTest' ? 'Comprehension Test' : 'Certification Test';\n\n        if (!res.Data.CanAttend) return;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  startQuiz(template) {\n    this.questionList = [];\n    this.examStarted = true;\n    const savedExam = LocalStorageHelper.get(this.examId + '~' + this.currentUser.Id);\n\n    if (savedExam) {\n      this.questionList = savedExam.QuestionList;\n      this.quiz = {\n        StartDate: savedExam.StartDate ? savedExam.StartDate : new Date().toISOString()\n      };\n      this.qIndex = savedExam.QIndex;\n      this.quizRunning = true;\n      this.allowAnswerSubmit = true;\n      this.timerSubscription = this.timer.start(savedExam.ExamTime).subscribe(status => {\n        if (status === 'ended') {\n          this.onTimesUp(template);\n          this.timerSubscription.unsubscribe();\n        }\n      });\n    } else {\n      this.blockUI.start('Starting exam. Please wait...');\n\n      this._service.get('exam/certificate-test/get-questions/' + this.examId).subscribe({\n        next: res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return;\n          } // **FIX: Use ISO string for proper datetime serialization**\n\n\n          this.quiz = {\n            StartDate: new Date().toISOString()\n          };\n          this.traineeEexamId = res.Data.TraineeExamId;\n          res.Data.MCQs.forEach(element => {\n            this.questionList.push({\n              Id: element.Id,\n              Question: element.Question,\n              Options: [{\n                Text: element.Option1,\n                Selected: false\n              }, {\n                Text: element.Option2,\n                Selected: false\n              }, {\n                Text: element.Option3,\n                Selected: false\n              }, {\n                Text: element.Option4,\n                Selected: false\n              }],\n              Mark: element.Mark,\n              Type: 'MCQ'\n            });\n          });\n          res.Data.TrueFalsQs.forEach(x => {\n            this.questionList.push({\n              Id: x.Id,\n              Question: x.Question,\n              Mark: x.Mark,\n              Answer: false,\n              CorrectAnswer: null,\n              Type: 'TFQ'\n            });\n          });\n          res.Data.FIGQs.forEach(x => {\n            this.questionList.push({\n              Id: x.Id,\n              Question: x.Question,\n              Mark: x.Mark,\n              Answer: null,\n              Type: 'FIGQ'\n            });\n          });\n\n          if (res.Data.MatchingQs) {\n            this.questionList.push({\n              LeftSides: res.Data.MatchingQs.LeftSides,\n              RightSides: res.Data.MatchingQs.RightSides,\n              Type: 'LRMQ'\n            });\n          }\n\n          res.Data.WrittenQs.forEach(x => {\n            this.questionList.push({\n              Id: x.Id,\n              Question: x.Question,\n              Mark: x.Mark,\n              Answer: null,\n              Type: 'WQ'\n            });\n          });\n          console.log(this.questionList);\n          this.qIndex = 0;\n          this.quizRunning = true;\n          this.allowAnswerSubmit = true;\n\n          if (this.allowAnswerSubmit) {\n            this.examTimeChangeScreen(template);\n          }\n\n          this.timerSubscription = this.timer.start(this.pagedetail.DurationMnt * 60).subscribe(status => {\n            if (status === 'ended') {\n              this.onTimesUp(template);\n              this.timerSubscription.unsubscribe();\n            }\n          });\n        },\n        error: err => {\n          this.toastr.error(err.message || err, 'Error!', {\n            closeButton: true,\n            disableTimeOut: true,\n            enableHtml: true\n          });\n          this.blockUI.stop();\n        },\n        complete: () => this.blockUI.stop()\n      }); // window.addEventListener('beforeunload', function (event) {\n      //   event.preventDefault();\n      // });\n      // window.addEventListener('unload', (event) => {\n      //   this.submitAnswer(this.template);\n      // });\n\n    }\n  } // @HostListener('window:beforeunload', ['$event'])\n  // onBeforeUnload(event: BeforeUnloadEvent): boolean {\n  //   alert();\n  //   event.returnValue = \"Your answer will be submitted\"\n  //   event.preventDefault();\n  //  return false;\n  //   // Show a confirmation message to the user\n  //   // const confirmationMessage = 'Are you sure you want to leave?';\n  //   // (event as any).returnValue = confirmationMessage; // Standard for most browsers\n  //   // return confirmationMessage; // For some older browsers\n  //   //this.saveAnswer(this.template)\n  // }\n\n\n  prepareDataToSubmit() {\n    this.examStarted = false;\n    let submittedMCQList = [],\n        submittedTFQList = [],\n        submittedFIGQList = [],\n        submittedLRMQList = [],\n        submittedWQList = [];\n    this.questionList.filter(x => x.Type === 'MCQ').forEach(element => {\n      submittedMCQList.push({\n        QuestionId: element.Id,\n        Answered: element.Options.map(function (x, i) {\n          if (x.Selected) return i + 1;else return 0;\n        }).filter(x => x > 0).join()\n      });\n    });\n    this.questionList.filter(x => x.Type === 'TFQ').forEach(element => {\n      submittedTFQList.push({\n        QuestionId: element.Id,\n        Answered: element.Answer,\n        CorrectAnswer: !element.Answer ? element.CorrectAnswer ? element.CorrectAnswer.trim() : element.CorrectAnswer : null\n      });\n    });\n    this.questionList.filter(x => x.Type === 'FIGQ').forEach(element => {\n      submittedFIGQList.push({\n        QuestionId: element.Id,\n        Answered: element.Answer ? element.Answer.trim() : element.Answer\n      });\n    });\n    let mQuestions = this.questionList.find(x => x.Type === 'LRMQ');\n    if (mQuestions) for (let i = 0; i < mQuestions.LeftSides.length; i++) {\n      const element = mQuestions.LeftSides[i];\n      submittedLRMQList.push({\n        QuestionId: element.Id,\n        Answered: mQuestions.RightSides[i]\n      });\n    }\n    this.questionList.filter(x => x.Type === 'WQ').forEach(element => {\n      submittedWQList.push({\n        QuestionId: element.Id,\n        Answered: element.Answer ? element.Answer.trim() : element.Answer\n      });\n    });\n    const data = {\n      ExamId: this.examId,\n      // **FIX: Use ISO strings for proper datetime serialization**\n      StartTime: this.quiz.StartDate,\n      EndTime: new Date().toISOString(),\n      AutoSubmission: true,\n      MCQList: submittedMCQList,\n      TFQList: submittedTFQList,\n      FIGQList: submittedFIGQList,\n      MatchingQList: submittedLRMQList,\n      WQList: submittedWQList,\n      Terminated: this.terminated\n    };\n    return data;\n  } // @HostListener('window:unload', ['$event'])\n  // unloadHandler(event: any): void {\n  //   let url=this.baseUrl+'exam/certificate-test/save-answers';\n  //   navigator.sendBeacon(url, this.prepareDataToSubmit());\n  // }\n\n\n  onPageChange(page) {\n    this.setPage(page);\n  }\n\n  setPage(pageNumber) {\n    this.page.pageNumber = pageNumber;\n    this.qIndex = pageNumber - 1;\n    this.btnNextText = this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';\n  }\n\n  drop(event) {\n    this.mqAnswered = true;\n    moveItemInArray(this.questionList[this.qIndex].RightSides, event.previousIndex, event.currentIndex);\n  }\n\n  onTimesUp(template) {\n    if (this.answerSubmitted) return; //  this.saveAnswerSheetIntoLocalStorage(true);\n\n    this.ngxSmartModalService.closeAll();\n    this.confirmService.close(); // this.saveAnswer(template, true);\n\n    this.confirmService.confirm(\"Time's Up!!\", 'Your answer will get submitted automatically. ' + this.answerStatus, 'OK', null, true).subscribe(() => {\n      this.answerSubmitted = true;\n      this.saveAnswer(template, true);\n    });\n  }\n\n  onScreenChange(template) {\n    //  this.saveAnswerSheetIntoLocalStorage(true);\n    if (this.answerSubmitted) {\n      console.log('Answer already submitted, skipping screen change handler');\n      return;\n    } // **FIX: Additional safety checks for terminated submissions**\n\n\n    if (!this.allowAnswerSubmit) {\n      console.log('Answer submission not allowed, forcing submission for terminated exam'); // For terminated exams, we still need to submit even if allowAnswerSubmit is false\n\n      this.answerSubmitted = true;\n      this.saveAnswer(template, true);\n      return;\n    }\n\n    this.ngxSmartModalService.closeAll();\n    this.confirmService.close();\n    this.confirmService.confirm('Your exam has been terminated!', 'Your answer will get submitted automatically. ' + this.answerStatus, 'OK', null, true).subscribe(() => {\n      this.answerSubmitted = true;\n      this.saveAnswer(template, true);\n    });\n  }\n\n  nextQuestion(template) {\n    if (this.qIndex < this.questionList.length - 1) this.qIndex++;else {\n      this.timer.pause();\n      this.submitAnswer(template);\n      return;\n    }\n    this.btnNextText = this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';\n    this.page.pageNumber = this.qIndex + 1;\n    this.pageService.currentPage.next(this.page.pageNumber);\n  }\n\n  prevQuestion() {\n    if (this.qIndex !== 0) this.qIndex--;\n    this.btnNextText = this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';\n    this.page.pageNumber = this.page.pageNumber - 1;\n    this.pageService.currentPage.next(this.page.pageNumber);\n  }\n\n  submitAnswer(template) {\n    if (this.answerSubmitted) return;\n    this.confirmService.confirm('Are you sure?', 'You are going to submit answer. ' + this.answerStatus, 'Yes, Submit Answer').subscribe(result => {\n      if (result) {\n        this.timer.pause();\n        this.answerSubmitted = true;\n        this.saveAnswer(template);\n      } else {\n        this.timer.resume();\n      }\n    });\n  }\n\n  saveAnswer(template, autoSubmission = false) {\n    var _a, _b, _c, _d, _e;\n\n    if (!this.isConnected) {\n      this.timer.resume();\n      return;\n    }\n\n    this.examStarted = false;\n    let submittedMCQList = [],\n        submittedTFQList = [],\n        submittedFIGQList = [],\n        submittedLRMQList = [],\n        submittedWQList = [];\n    this.questionList.filter(x => x.Type === 'MCQ').forEach(element => {\n      submittedMCQList.push({\n        QuestionId: element.Id,\n        Answered: element.Options.map(function (x, i) {\n          if (x.Selected) return i + 1;else return 0;\n        }).filter(x => x > 0).join()\n      });\n    });\n    this.questionList.filter(x => x.Type === 'TFQ').forEach(element => {\n      submittedTFQList.push({\n        QuestionId: element.Id,\n        Answered: element.Answer,\n        CorrectAnswer: !element.Answer ? element.CorrectAnswer ? element.CorrectAnswer.trim() : element.CorrectAnswer : null\n      });\n    });\n    this.questionList.filter(x => x.Type === 'FIGQ').forEach(element => {\n      submittedFIGQList.push({\n        QuestionId: element.Id,\n        Answered: element.Answer ? element.Answer.trim() : element.Answer\n      });\n    });\n    let mQuestions = this.questionList.find(x => x.Type === 'LRMQ');\n    if (mQuestions) for (let i = 0; i < mQuestions.LeftSides.length; i++) {\n      const element = mQuestions.LeftSides[i];\n      submittedLRMQList.push({\n        QuestionId: element.Id,\n        Answered: mQuestions.RightSides[i]\n      });\n    }\n    this.questionList.filter(x => x.Type === 'WQ').forEach(element => {\n      submittedWQList.push({\n        QuestionId: element.Id,\n        Answered: element.Answer ? element.Answer.trim() : element.Answer\n      });\n    });\n    const obj = {\n      ExamId: this.examId,\n      TraineeExamId: this.traineeEexamId,\n      // **FIX: Use ISO strings for proper datetime serialization**\n      StartTime: this.quiz.StartDate,\n      EndTime: new Date().toISOString(),\n      AutoSubmission: autoSubmission,\n      MCQList: submittedMCQList,\n      TFQList: submittedTFQList,\n      FIGQList: submittedFIGQList,\n      MatchingQList: submittedLRMQList,\n      WQList: submittedWQList,\n      Terminated: this.terminated\n    }; // **FIX: Log terminated submissions for debugging**\n\n    if (this.terminated) {\n      console.log('TERMINATED SUBMISSION:', {\n        terminated: this.terminated,\n        autoSubmission: autoSubmission,\n        mcqCount: ((_a = obj.MCQList) === null || _a === void 0 ? void 0 : _a.length) || 0,\n        tfqCount: ((_b = obj.TFQList) === null || _b === void 0 ? void 0 : _b.length) || 0,\n        figCount: ((_c = obj.FIGQList) === null || _c === void 0 ? void 0 : _c.length) || 0,\n        matchingCount: ((_d = obj.MatchingQList) === null || _d === void 0 ? void 0 : _d.length) || 0,\n        writtenCount: ((_e = obj.WQList) === null || _e === void 0 ? void 0 : _e.length) || 0\n      });\n    }\n\n    this.blockUI.start('Submitting answer. Please wait...');\n\n    this._service.post('exam/certificate-test/save-answers', obj).subscribe({\n      next: res => {\n        this.timerSubscription.unsubscribe();\n\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.error(res.Message, 'Warning!', {\n            closeButton: true,\n            disableTimeOut: true,\n            enableHtml: true\n          });\n          console.log(res.Data);\n          this.timer.resume(); // return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: true,\n            enableHtml: true\n          }); //this.timer.resume();\n\n          console.log(res.Data);\n          this.timerSubscription.unsubscribe(); // this._location.back();\n          // return;\n        } // this._location.back();\n\n\n        this.confirmService.close();\n        this.toastr.success(res.Message, 'Answer Submission!', {\n          timeOut: 4000\n        });\n        this.allowAnswerSubmit = false;\n        if (!res.Data) this._location.back();else {\n          this.result = res.Data;\n          this.modalRef = this.modalService.show(template, this.modalConfig);\n        }\n      },\n      error: err => {\n        // this._location.back();\n        console.log(err); // this.timer.resume();\n\n        this.timerSubscription.unsubscribe();\n        this.toastr.error(err.message || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: true,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => {\n        this.blockUI.stop();\n      }\n    });\n  }\n\n  downloadCertificate(item) {\n    this.blockUI.start('Generating certificate. Please wait...');\n\n    this._service.downloadFile('course/download-certificate/' + item).subscribe({\n      next: res => {\n        // this.pdfViewerOnDemand.pdfSrc = res; // pdfSrc can be Blob or Uint8Array\n        // this.pdfViewerOnDemand.refresh();\n        // this.ngxSmartModalService.create('certificateModal', this.tpl).open();\n        const url = window.URL.createObjectURL(res);\n        var link = document.createElement('a');\n        link.href = url;\n        link.target = 'blank';\n        link.rel = 'noopener';\n        link.download = item.Title + ' Certificate.pdf';\n        link.click();\n      },\n      error: err => {\n        this.toastr.error(err.message || err, 'Error!', {\n          timeOut: 2000\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  backClicked(examStarted) {\n    if (!examStarted) {\n      this._location.back();\n    } else {\n      this.confirmService.confirm('Warning!', 'Your answer will get submitted automatically. ' + this.answerStatus, 'OK', 'CANCEL', false).subscribe(res => {\n        if (res) {\n          this.saveAnswer(this.template);\n\n          this._location.back();\n        }\n      });\n    }\n  }\n\n  examTimeChangeScreen(template) {\n    document.addEventListener('visibilitychange', event => {\n      if (document.visibilityState != 'visible') {\n        if (this.changeScreenCounter === 0 && this.allowAnswerSubmit == true) {\n          alert('You are not allowed to change the screen!!! If you try one more step to change the screen the exam will be terminated!!!');\n          this.changeScreenCounter++;\n        } else if (this.changeScreenCounter === 1 && this.allowAnswerSubmit == true) {\n          // **FIX: Prevent race conditions in terminated submission**\n          // Set terminated flag first\n          this.terminated = true; // Stop timer and quiz state immediately to prevent conflicts\n\n          if (this.timerSubscription) {\n            this.timerSubscription.unsubscribe();\n          }\n\n          this.quizRunning = false;\n          this.allowAnswerSubmit = false;\n          this.changeScreenCounter++; // Call screen change handler after state is stabilized\n\n          this.onScreenChange(template);\n        }\n      }\n    });\n  }\n\n  tryAgain() {\n    this.modalHide();\n  }\n\n  modalHide() {\n    this.modalRef.hide();\n\n    this._location.back();\n  }\n\n}\n\nCourseCertificateTestComponent.ɵfac = function CourseCertificateTestComponent_Factory(t) {\n  return new (t || CourseCertificateTestComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.NgxSmartModalService), i0.ɵɵdirectiveInject(i4.BsModalService), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.ConfirmService), i0.ɵɵdirectiveInject(i7.AuthenticationService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i8.PaginationService), i0.ɵɵdirectiveInject(i9.Location));\n};\n\nCourseCertificateTestComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CourseCertificateTestComponent,\n  selectors: [[\"app-course-certificate-test\"]],\n  viewQuery: function CourseCertificateTestComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(TemplateRef, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fullScreenElement = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tpl = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n    }\n  },\n  decls: 6,\n  vars: 1,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"row\"], [\"class\", \"col p-5\", 3, \"ngClass\", 4, \"ngIf\"], [\"template\", \"\"], [1, \"col\", \"p-5\", 3, \"ngClass\"], [1, \"row\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"col-12\"], [1, \"pt-2\", \"p-md-3\"], [1, \"col-12\", \"d-flex\", \"justify-content-between\"], [1, \"h3\", \"text-break\", \"mb-0\"], [1, \"fw-bold\", \"text-uppercase\", \"fs-6\"], [1, \"btn\", \"btn-link\", \"text-decoration-none\", \"fs-4\", \"fw-bold\", \"btn-sm\", \"d-flex\", \"align-items-center\", \"border-start\", 3, \"click\"], [1, \"fs-4\", \"ai-arrow-left\", \"fs-base\", \"me-2\"], [\"class\", \"col-12 mb-3\", 4, \"ngIf\"], [1, \"col-12\", \"mb-3\"], [\"class\", \"section\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"totalItems\", \"paginationControlToShow\", \"itemsPerPage\", \"definitions\", \"currentPage\", \"mqAnswered\", \"pageChange\"], [1, \"section\"], [\"class\", \"col-12\", 4, \"ngIf\"], [\"class\", \"col-12 text-center\", 4, \"ngIf\"], [1, \"widget\", \"widget_recent_post\"], [1, \"col-lg-6\", \"col-12\"], [1, \"list_none\", \"blog_meta\"], [1, \"fa\", \"fa-arrow-right\", \"me-2\"], [1, \"list_none\", \"blog_meta\", \"mb-4\", \"mt-2\"], [1, \"text-primary\"], [3, \"isAnimated\"], [\"heading\", \"EXAM INSTRUCTIONS (click here to see)\", \"panelClass\", \"custom-accordion\"], [3, \"innerHTML\"], [1, \"col-12\", \"text-center\"], [\"class\", \"bg-success rounded-3\", 4, \"ngIf\"], [\"class\", \"bg-danger rounded-3\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"bg-success\", \"rounded-3\"], [1, \"p-3\", \"text-white\"], [1, \"fa\", \"fa-award\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", \"btn-sm\", \"text-decoration-none\", \"text-white\", 3, \"click\"], [1, \"fa-solid\", \"fa-download\"], [1, \"bg-danger\", \"rounded-3\"], [1, \"fa\", \"fa-exclamation-triangle\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-check\"], [1, \"card\", \"q-panel\", \"noselect\"], [1, \"card-header\"], [1, \"col-lg-8\", \"col-md-6\", \"col-12\", \"qs-counter\"], [1, \"fa-solid\", \"fa-circle-question\"], [1, \"col-lg-4\", \"col-md-6\", \"col-12\", \"text-end\", \"timer\"], [1, \"fa-solid\", \"fa-clock\"], [\"class\", \"question-body noselect\", 4, \"ngIf\"], [1, \"card-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"btn-box\", \"me-1\", 3, \"disabled\", \"click\"], [1, \"fa\", \"fa-arrow-left\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", 3, \"click\"], [1, \"fa\", \"fa-arrow-right\"], [1, \"question-body\", \"noselect\"], [\"class\", \"card-body\", 4, \"ngIf\"], [\"class\", \"list-group\", 4, \"ngIf\"], [1, \"card-body\"], [1, \"col-sm-11\", \"col-10\"], [1, \"text-question\", \"mb-0\"], [1, \"col-sm-1\", \"col-2\"], [1, \"list-group\"], [\"class\", \"list-group-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\"], [\"type\", \"radio\", 1, \"form-check-input\", 3, \"name\", \"ngModel\", \"id\", \"value\", \"click\", \"ngModelChange\"], [1, \"form-check-label\", 3, \"for\"], [1, \"col-sm-12\", \"mt-2\"], [\"labelOff\", \"False\", \"labelOn\", \"True\", \"defaultBgColor\", \"#E82D4C\", 3, \"ngModel\", \"ngModelChange\"], [1, \"input-group\", \"mb-3\"], [1, \"input-group-text\"], [\"type\", \"text\", \"placeholder\", \"Write answer ..\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"card\"], [1, \"text-bold\"], [1, \"card-body\", \"pt-3\"], [1, \"col-sm-7\", \"col-12\", \"pb-2\"], [\"class\", \"row\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-sm-5\", \"col-12\"], [\"cdkDropList\", \"\", 1, \"example-list\", 3, \"cdkDropListDropped\"], [\"class\", \"example-box\", \"cdkDrag\", \"\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-sm-11\", \"col-xs-11\", \"example-box\"], [1, \"col-sm-1\", \"col-xs-1\", \"example-box\"], [\"cdkDrag\", \"\", 1, \"example-box\"], [\"class\", \"example-custom-placeholder\", 4, \"cdkDragPlaceholder\"], [1, \"example-custom-placeholder\"], [\"rows\", \"5\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"cc-style-1\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn\", \"btn-sm\", \"close\", \"pull-right\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-header\", \"d-flex\", \"justify-content-center\"], [1, \"modal-body\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"modal-footer\", \"d-flex\", \"justify-content-center\", \"py-1\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"text-center\"], [\"src\", \"./assets/img/CertificateCongratulations.gif\", \"alt\", \"\", 4, \"ngIf\"], [\"src\", \"./assets/img/CertificateCongratulations.gif\", \"alt\", \"\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n  template: function CourseCertificateTestComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵtemplate(3, CourseCertificateTestComponent_div_3_Template, 17, 6, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, CourseCertificateTestComponent_ng_template_4_Template, 13, 8, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.pagedetail);\n    }\n  },\n  directives: [i10.BlockUIComponent, i9.NgIf, i9.NgClass, i11.DefaultClassDirective, i12.PaginationComponent, i13.AccordionComponent, i13.AccordionPanelComponent, i9.NgForOf, i14.RadioControlValueAccessor, i14.DefaultValueAccessor, i14.NgControlStatus, i14.NgModel, i15.UiSwitchComponent, i16.CdkDropList, i16.CdkDrag, i16.CdkDragPlaceholder],\n  pipes: [i17.DateFormatPipe, i17.LocalTimePipe, i17.FromUtcPipe, i18.SafePipe],\n  styles: [\".example-list[_ngcontent-%COMP%]{width:100%;max-width:100%;border:solid 1px #ccc;display:block;background:white;border-radius:4px;overflow:hidden}.example-box[_ngcontent-%COMP%]{padding:10px;border-bottom:solid 1px #ccc;color:#000000de;display:flex;flex-direction:row;align-items:center;justify-content:space-between;box-sizing:border-box;cursor:move;background:white;font-size:14px}.cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px #0003,0 8px 10px 1px #00000024,0 3px 14px 2px #0000001f}.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.example-box[_ngcontent-%COMP%]:last-child{border:none}.example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.example-custom-placeholder[_ngcontent-%COMP%]{background:#ccc;border:dotted 3px #999;min-height:60px;transition:transform .25s cubic-bezier(0,0,.2,1)}.text-question[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#423838}.timer[_ngcontent-%COMP%]{font-size:16px;color:red;text-align:right}.timer[_ngcontent-%COMP%]   b[_ngcontent-%COMP%]{font-weight:900}@media (max-width: 768px){.timer[_ngcontent-%COMP%]{text-align:center;margin-top:5px;border-top:1px solid #8383a1;display:block}}.q-panel[_ngcontent-%COMP%], .q-panel[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#d7f4ff;font-size:19px;font-weight:600;color:#423838}.q-panel[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%]{font-size:19px!important;color:#423838}.cc-style-1[_ngcontent-%COMP%]{background-image:linear-gradient(#E9EFF5,#BBE1F7,#6EABF7);border-radius:15px}.cc-style-2[_ngcontent-%COMP%]{font-style:italic}\"],\n  data: {\n    animation: [trigger('inOutAnimation', [transition(':enter', [style({\n      height: 0,\n      opacity: 0\n    }), animate('0.5s ease-out', style({\n      height: 50,\n      opacity: 1\n    }))]), transition(':leave', [style({\n      height: 50,\n      opacity: 1\n    }), animate('0.5s ease-in', style({\n      height: 0,\n      opacity: 0\n    }))])])]\n  }\n});\n\n__decorate([BlockUI()], CourseCertificateTestComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}