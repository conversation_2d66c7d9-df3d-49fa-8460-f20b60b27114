﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{BF7522BF-69E2-471F-97A5-AD3B53E8FE0A}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Brac.LMS.App.API</RootNamespace>
    <AssemblyName>Brac.LMS.App.API</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44369</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AngleSharp, Version=********, Culture=neutral, PublicKeyToken=e83494dcdc6d31ea, processorArchitecture=MSIL">
      <HintPath>..\packages\AngleSharp.0.16.1\lib\net472\AngleSharp.dll</HintPath>
    </Reference>
    <Reference Include="AngleSharp.Css, Version=0.16.3.0, Culture=neutral, PublicKeyToken=e83494dcdc6d31ea, processorArchitecture=MSIL">
      <HintPath>..\packages\AngleSharp.Css.0.16.3\lib\net472\AngleSharp.Css.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Core, Version=1.30.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Core.1.30.0\lib\net461\Azure.Core.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.8.9.0, Culture=neutral, PublicKeyToken=0e99375e54769942">
      <HintPath>..\packages\BouncyCastle.1.8.9\lib\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=1.60.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.1.60.6\lib\net451\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FastReport">
      <HintPath>..\Libraries\FastReport.dll</HintPath>
    </Reference>
    <Reference Include="FastReport.Bars, Version=2019.4.9.0, Culture=neutral, PublicKeyToken=db7e5ce63278458c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Libraries\FastReport.Bars.dll</HintPath>
    </Reference>
    <Reference Include="FastReport.Editor, Version=2019.4.9.0, Culture=neutral, PublicKeyToken=db7e5ce63278458c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Libraries\FastReport.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.6.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=3.6.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Graph, Version=5.4.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Graph.5.4.0\lib\netstandard2.0\Microsoft.Graph.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Graph.Core, Version=3.0.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Graph.Core.3.0.4\lib\net462\Microsoft.Graph.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=6.27.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Abstractions.6.27.0\lib\net472\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=6.27.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.6.27.0\lib\net472\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=6.27.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.6.27.0\lib\net472\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols, Version=6.27.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.6.27.0\lib\net472\Microsoft.IdentityModel.Protocols.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect, Version=6.27.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.OpenIdConnect.6.27.0\lib\net472\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=6.27.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.6.27.0\lib\net472\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Abstractions, Version=1.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Abstractions.1.1.0\lib\netstandard2.0\Microsoft.Kiota.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Authentication.Azure, Version=1.0.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Authentication.Azure.1.0.2\lib\netstandard2.0\Microsoft.Kiota.Authentication.Azure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Http.HttpClientLibrary, Version=1.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Http.HttpClientLibrary.1.0.1\lib\netstandard2.0\Microsoft.Kiota.Http.HttpClientLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Serialization.Form, Version=1.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Serialization.Form.1.0.1\lib\netstandard2.0\Microsoft.Kiota.Serialization.Form.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Serialization.Json, Version=1.0.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Serialization.Json.1.0.3\lib\netstandard2.0\Microsoft.Kiota.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Serialization.Text, Version=1.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Serialization.Text.1.0.1\lib\netstandard2.0\Microsoft.Kiota.Serialization.Text.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.2.0\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Cors.4.2.0\lib\net45\Microsoft.Owin.Cors.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.4.2.0\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.4.2.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.4.2.0\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Facebook, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Facebook.4.2.0\lib\net45\Microsoft.Owin.Security.Facebook.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Google, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Google.4.2.0\lib\net45\Microsoft.Owin.Security.Google.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.MicrosoftAccount, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.MicrosoftAccount.4.2.0\lib\net45\Microsoft.Owin.Security.MicrosoftAccount.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.4.2.0\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Twitter, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Twitter.4.2.0\lib\net45\Microsoft.Owin.Security.Twitter.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Data" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.6.0.0\lib\net461\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=6.27.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.6.27.0\lib\net472\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=1.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.1.0.2\lib\net461\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WinHttpHandler, Version=7.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.WinHttpHandler.7.0.0\lib\net462\System.Net.Http.WinHttpHandler.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Claims, Version=4.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Claims.4.3.0\lib\net46\System.Security.Claims.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.CodePages, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.5.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.6.0.0\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.6.0.0\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.2.7\lib\net45\System.Web.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Cors.5.2.7\lib\net45\System.Web.Http.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.7\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="Tavis.UriTemplates, Version=2.0.0.0, Culture=neutral, PublicKeyToken=5be2d48fa8a60581, processorArchitecture=MSIL">
      <HintPath>..\packages\Tavis.UriTemplates.2.0.0\lib\netstandard2.0\Tavis.UriTemplates.dll</HintPath>
    </Reference>
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebApiThrottle, Version=1.5.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\WebApiThrottle.1.5.4\lib\net45\WebApiThrottle.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.AspNet.Identity.Core">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.3\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.3\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework">
      <HintPath>..\packages\Microsoft.AspNet.Identity.EntityFramework.2.2.3\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="Owin">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.4.0.1\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Owin">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Owin.5.2.7\lib\net45\System.Web.Http.Owin.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\Startup.Auth.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Areas\HelpPage\ApiDescriptionExtensions.cs" />
    <Compile Include="Areas\HelpPage\App_Start\HelpPageConfig.cs" />
    <Compile Include="Areas\HelpPage\Controllers\HelpController.cs" />
    <Compile Include="Areas\HelpPage\HelpPageAreaRegistration.cs" />
    <Compile Include="Areas\HelpPage\HelpPageConfigurationExtensions.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\CollectionModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ComplexTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\DictionaryModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumValueDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\IModelDocumentationProvider.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\KeyValuePairModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescriptionGenerator.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameAttribute.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameHelper.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterAnnotation.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\SimpleTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\Models\HelpPageApiModel.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleKey.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ImageSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\InvalidSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ObjectGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\SampleDirection.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\TextSample.cs" />
    <Compile Include="Areas\HelpPage\XmlDocumentationProvider.cs" />
    <Compile Include="Controllers\CourseCategoryController.cs" />
    <Compile Include="Controllers\CourseSegmentController.cs" />
    <Compile Include="Controllers\ErrorPageController.cs" />
    <Compile Include="Controllers\ExamController.cs" />
    <Compile Include="Controllers\ExternalCourseController.cs" />
    <Compile Include="Controllers\ForumController.cs" />
    <Compile Include="Controllers\GhooriCertificateController.cs" />
    <Compile Include="Controllers\LearningHourCategoryController.cs" />
    <Compile Include="Controllers\LibraryCategoryController.cs" />
    <Compile Include="Controllers\NotificationController.cs" />
    <Compile Include="Controllers\TraineeDeviceController.cs" />
    <Compile Include="Extension\SecurityProtocolTypeExtensions.cs" />
    <Compile Include="Extension\SslProtocolsExtensions.cs" />
    <Compile Include="Helpers\AntiXssMiddleware.cs" />
    <Compile Include="Controllers\CertificateConfigurationController.cs" />
    <Compile Include="Controllers\ConfigurationController.cs" />
    <Compile Include="Controllers\EvaluationExamController.cs" />
    <Compile Include="Controllers\FeedbackController.cs" />
    <Compile Include="Controllers\LibraryController.cs" />
    <Compile Include="Controllers\OpenMaterialController.cs" />
    <Compile Include="Controllers\DashboardController.cs" />
    <Compile Include="Controllers\DepartmentController.cs" />
    <Compile Include="Controllers\DivisionController.cs" />
    <Compile Include="Controllers\FAQController.cs" />
    <Compile Include="Controllers\ForumCategoryController.cs" />
    <Compile Include="Controllers\CourseController.cs" />
    <Compile Include="Controllers\GradingPolicyController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\AccountController.cs" />
    <Compile Include="Controllers\ApplicationController.cs" />
    <Compile Include="Controllers\SanitizeAttribute.cs" />
    <Compile Include="Controllers\TraineeController.cs" />
    <Compile Include="Controllers\SubUnitController.cs" />
    <Compile Include="Controllers\UnitController.cs" />
    <Compile Include="Controllers\UserGroupController.cs" />
    <Compile Include="Filters\CustomAuthorize.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Models\AccountBindingModels.cs" />
    <Compile Include="Models\AccountViewModels.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Providers\ApplicationOAuthProvider.cs" />
    <Compile Include="Results\ChallengeResult.cs" />
    <Compile Include="Startup.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Data\box.png" />
    <Content Include="App_Data\certificate.jpg" />
    <Content Include="App_Data\Certificate.xml" />
    <Content Include="App_Data\TemplateBG\Template_1.jpg" />
    <Content Include="App_Data\Template_1.jpg" />
    <Content Include="App_Data\Template_1.xml" />
    <Content Include="App_Data\checked_box.jpg" />
    <Content Include="Areas\HelpPage\HelpPage.css" />
    <Content Include="Content\bootstrap-grid.css" />
    <Content Include="Content\bootstrap-grid.min.css" />
    <Content Include="Content\bootstrap-grid.rtl.css" />
    <Content Include="Content\bootstrap-grid.rtl.min.css" />
    <Content Include="Content\bootstrap-reboot.css" />
    <Content Include="Content\bootstrap-reboot.min.css" />
    <Content Include="Content\bootstrap-reboot.rtl.css" />
    <Content Include="Content\bootstrap-reboot.rtl.min.css" />
    <Content Include="Content\bootstrap-utilities.css" />
    <Content Include="Content\bootstrap-utilities.min.css" />
    <Content Include="Content\bootstrap-utilities.rtl.css" />
    <Content Include="Content\bootstrap-utilities.rtl.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\bootstrap.rtl.css" />
    <Content Include="Content\bootstrap.rtl.min.css" />
    <Content Include="favicon.ico" />
    <Content Include="Files\CourseMaterial\Documents\BBL-Alo.png" />
    <!-- <Content Include="Files\CourseMaterial\Videos\FilePath.mp4" />
    <Content Include="Files\CourseMaterial\Videos\SampleVideo_720x480_10mb.mp4" />
    <Content Include="Files\CourseMaterial\Videos\SampleVideo_720x480_2mb.mp4" />
    <Content Include="Files\CourseMaterial\Videos\SampleVideo_720x480_5mb.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDCIDZ20220126131529.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDCJQO20220330205318.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDETTQ20221122023117.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDFWQC20220807022130.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDNIMT20220330205307.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDNPKH20220330204625.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDOIRN20220330205248.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDPKWE20220227094341.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDSFNB20220124160750.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDSOJF20221122023412.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDUDAG20221122023056.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDUKIJ20221106164815.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDVQQK20221106164759.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDWDCM20221122023348.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDXXKI20230101124059.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDZXDZ20220227093436.mp4" />
    <Content Include="Files\CourseMaterial\Videos\VIDZXZG20220126131832.mp4" />
    <Content Include="Files\EmailTemplates\account-create.html" />
    <Content Include="Files\EmailTemplates\notification-for-enrollment.html" />
    <Content Include="Files\EmailTemplates\notification.html" />
    <Content Include="Files\EmailTemplates\notification_generic.html" />
    <Content Include="Files\EmailTemplates\notification_x.html" />
    <Content Include="Files\EmailTemplates\password-reset.html" />
    <Content Include="Files\EvaluationExam\Images\220207000_PDG.jpg" />
    <Content Include="Files\EvaluationExam\Images\220904000_TID.png" />
    <Content Include="Files\EvaluationExam\Images\220904000_WBS.png" />
    <Content Include="Files\EvaluationExam\Images\221016000_GKA.jpeg" />
    <Content Include="Files\EvaluationExam\Images\221203000_WNM.jpg" />
    <Content Include="Files\EvaluationExam\Images\221205000_IWX.jpg" />
    <Content Include="Files\EvaluationExam\Images\221205000_SHR.png" />
    <Content Include="Files\EvaluationExam\Images\230406000_BDI.jpg" />
    <Content Include="Files\EvaluationExam\Images\230510000_WOH.png" />
    <Content Include="Files\EvaluationExam\Images\230613000_JAC.jpg" />
    <Content Include="Files\Library\Images\220222000_DFF.png" />
    <Content Include="Files\Library\Images\220222000_ORB.jpg" />
    <Content Include="Files\Library\Images\220227000_CSK.jpg" />
    <Content Include="Files\Library\Images\220227000_YMI.jpg" />
    <Content Include="Files\Library\Images\220309000_PWX.jpg" />
    <Content Include="Files\Library\Images\221217000_ENH.png" />
    <Content Include="Files\Library\Videos\VIDTSDV20221217155224.mp4" />
    <Content Include="Files\Library\Videos\VIDYIIX20220222151042-1.mp4" />
    <Content Include="Files\Library\Videos\VIDYIIX20220222151042.mp4" />
    <Content Include="Files\OpenMaterial\Images\220207000_CLF.jpg" />
    <Content Include="Files\OpenMaterial\Images\220207000_HSF.jpg" />
    <Content Include="Files\OpenMaterial\Images\220220000_KSF.jpg" />
    <Content Include="Files\OpenMaterial\Images\221203000_DOK.jpg" />
    <Content Include="Files\OpenMaterial\Images\221205000_YWF.jpg" />
    <Content Include="Files\OpenMaterial\Videos\VIDZOGG20220220014103.mp4" /> -->
    <Content Include="Files\Users\8091ebd0-5c9c-4320-bfb5-0ee8cb91e974_OZX.jpg" />
    <Content Include="Global.asax" />
    <Content Include="Areas\HelpPage\Views\Web.config" />
    <Content Include="Areas\HelpPage\Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\ResourceModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Index.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\TextSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\SimpleTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Samples.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Parameters.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ModelDescriptionLink.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\KeyValuePairModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\InvalidSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ImageSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\HelpPageApiModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\EnumTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\DictionaryModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ComplexTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\CollectionModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ApiGroup.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Api.cshtml" />
    <Content Include="Files\UploadSamples\course_exam_figq_sample_file.xlsx" />
    <Content Include="Files\UploadSamples\course_exam_lrmq_sample_file.xlsx" />
    <Content Include="Files\UploadSamples\course_exam_mcq_sample_file.xlsx" />
    <Content Include="Files\UploadSamples\course_exam_tfq_sample_file.xlsx" />
    <Content Include="Files\UploadSamples\course_exam_wq_sample_file.xlsx" />
    <Content Include="Files\UploadSamples\grading_policy.xlsx" />
    <Content Include="Files\UploadSamples\trainee.xlsx" />
    <Content Include="Files\UploadSamples\trainer.xlsx" />
    <Content Include="App_Data\Template_1.frx" />
    <Content Include="Content\bootstrap.rtl.min.css.map" />
    <Content Include="Content\bootstrap.rtl.css.map" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-utilities.rtl.min.css.map" />
    <Content Include="Content\bootstrap-utilities.rtl.css.map" />
    <Content Include="Content\bootstrap-utilities.min.css.map" />
    <Content Include="Content\bootstrap-utilities.css.map" />
    <Content Include="Content\bootstrap-reboot.rtl.min.css.map" />
    <Content Include="Content\bootstrap-reboot.rtl.css.map" />
    <Content Include="Content\bootstrap-reboot.min.css.map" />
    <Content Include="Content\bootstrap-reboot.css.map" />
    <Content Include="Content\bootstrap-grid.rtl.min.css.map" />
    <Content Include="Content\bootstrap-grid.rtl.css.map" />
    <Content Include="Content\bootstrap-grid.min.css.map" />
    <Content Include="Content\bootstrap-grid.css.map" />
    <!-- <Content Include="Files\UploadSamples\course_enrolment_sample_file.xlsx" />
    <Content Include="Files\UploadSamples\guest_trainee_sample_file.xlsx" />
    <Content Include="Files\OpenMaterial\Documents\DOCSMPA20220622033414.pdf" />
    <Content Include="Files\OpenMaterial\Documents\DOCTTAQ20220207155150.pdf" />
    <Content Include="Files\OpenMaterial\Documents\DOCUWFI20221203124503.pdf" />
    <Content Include="Files\OpenMaterial\Documents\DOCYAAV20220207155249.pdf" />
    <Content Include="Files\OpenMaterial\Documents\DOCYWFT20221205095331.pdf" />
    <Content Include="Files\Configuration\Documents\DOCAENT20230822152231.pdf" />
    <Content Include="Files\Configuration\Infos\DOCFHEJ20230822152231.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCATUM20230822114844.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCBHWS20220227093646.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCDXCK20211207145404.xlsx" />
    <Content Include="Files\CourseMaterial\Documents\DOCEBBV20230822114936.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCFVHD20230822114532.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCGIYS20220227094506.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCHPDP20220620221900.ppt" />
    <Content Include="Files\CourseMaterial\Documents\DOCIICQ20220126131730.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCKWEC20220227094540.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCLHZN20220904033935.docx" />
    <Content Include="Files\CourseMaterial\Documents\DOCMFPV20220420220516.ppt" />
    <Content Include="Files\CourseMaterial\Documents\DOCMFPV20220420220516.pptx" />
    <Content Include="Files\CourseMaterial\Documents\DOCOHGJ20211128154159.xlsx" />
    <Content Include="Files\CourseMaterial\Documents\DOCPALP20211207145356.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCPGGB20220530151436.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCQLKH20220124160008.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCQLNL20230822113421.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCQTUA20220126131657.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCRICC20230822114916.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCTJYO20220124155810.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCUSSY20220126131745.pdf" />
    <Content Include="Files\CourseMaterial\Documents\DOCWTES20220411223025.pdf" />
    <Content Include="Files\CourseMaterial\Documents\file_example_PPT_500kB.ppt" />
    <Content Include="Files\CourseMaterial\Documents\REPORT Sample.xlsx" />
    <Content Include="Files\CourseMaterial\Documents\~%24REPORT Sample.xlsx" />
    <Content Include="Files\GhooriCertificate\DOCBGPF20230806145228.pdf" />
    <Content Include="Files\GhooriCertificate\DOCCYHY20230806144905.pdf" />
    <Content Include="Files\GhooriCertificate\DOCDSOQ20230809083238.pdf" />
    <Content Include="Files\GhooriCertificate\DOCDVFF20230807073514.pdf" />
    <Content Include="Files\GhooriCertificate\DOCFNBP20230806024247.pdf" />
    <Content Include="Files\GhooriCertificate\DOCFRCS20230806030034.pdf" />
    <Content Include="Files\GhooriCertificate\DOCGJYL20230807072028.pdf" />
    <Content Include="Files\GhooriCertificate\DOCGUFL20230807074125.pdf" />
    <Content Include="Files\GhooriCertificate\DOCHRKK20230807074525.pdf" />
    <Content Include="Files\GhooriCertificate\DOCJPEB20230806021313.pdf" />
    <Content Include="Files\GhooriCertificate\DOCKSJY20230808221746.pdf" />
    <Content Include="Files\GhooriCertificate\DOCPRUJ20230806030018.pdf" />
    <Content Include="Files\GhooriCertificate\DOCQTAD20230806144656.pdf" />
    <Content Include="Files\GhooriCertificate\DOCRLNU20230806230532.pdf" />
    <Content Include="Files\GhooriCertificate\DOCTICU20230807073448.pdf" />
    <Content Include="Files\GhooriCertificate\DOCUYYL20230807073501.pdf" />
    <Content Include="Files\GhooriCertificate\DOCXBHI20230807072017.pdf" />
    <Content Include="Files\Library\Documents\DOCJAYY20220313230012.pdf" />
    <Content Include="Files\Library\Documents\DOCLBMQ20220222151214.pdf" />
    <Content Include="Files\MaterialResource\DOCAMWF20220126150806.pdf" />
    <Content Include="Files\MaterialResource\DOCDPXY20220126150752.pdf" /> -->
    <Content Include="Areas\HelpPage\Views\Shared\Error.cshtml" />
    <Content Include="Areas\HelpPage\Views\Shared\NotFound.cshtml" />
    <Content Include="App_Data\Certificate.frx" />
    <!-- <Content Include="Images\Admin\20220212125641_KNW.jpeg" />
    <Content Include="Images\Admin\20220217201438_AIY.jpeg" />
    <Content Include="Images\Admin\20220222014246_IFB.jpg" />
    <Content Include="Images\Admin\20220227024411_RIH.jpeg" />
    <Content Include="Images\Admin\20220227205338_WKX.jpg" />
    <Content Include="Images\Admin\20220228195226_OIV.jpg" />
    <Content Include="Images\Admin\20220330231107_SWU.jpg" />
    <Content Include="Images\Admin\20220330231210_KML.jpg" />
    <Content Include="Images\Admin\20220412234721_XIC.png" />
    <Content Include="Images\Admin\20220515231231_PEL.jpg" />
    <Content Include="Images\Admin\20220518215235_LSU.png" />
    <Content Include="Images\Admin\20220606223834_VLF.jpg" />
    <Content Include="Images\Admin\20220620224346_OYR.jpg" />
    <Content Include="Images\Admin\20221229122513_NDC.png" />
    <Content Include="Images\CertificateConfiguration\55ca838c-b37f-49f7-a9b4-2123ba53d225\Person1Sign.png" />
    <Content Include="Images\CertificateConfiguration\5b8fef47-5cfc-4a7a-9987-fe8b40c045cf\Person1Sign.png" />
    <Content Include="Images\CertificateConfiguration\5b8fef47-5cfc-4a7a-9987-fe8b40c045cf\Person2Sign.png" />
    <Content Include="Images\CertificateConfiguration\5b8fef47-5cfc-4a7a-9987-fe8b40c045cf\Person3Sign.png" />
    <Content Include="Images\CertificateConfiguration\5b8fef47-5cfc-4a7a-9987-fe8b40c045cf\TemplatePath.jpg" />
    <Content Include="Images\CertificateConfiguration\80f58d58-85fb-49ad-a8a3-a2c1b976aff4\Person1Sign.png" />
    <Content Include="Images\CertificateConfiguration\80f58d58-85fb-49ad-a8a3-a2c1b976aff4\Person3Sign.png" />
    <Content Include="Images\CertificateConfiguration\80f58d58-85fb-49ad-a8a3-a2c1b976aff4\TemplatePath.jpg" />
    <Content Include="Images\CertificateConfiguration\da935def-f80f-453a-b1ed-951b4dcdff48\Person1Sign.png" />
    <Content Include="Images\CertificateConfiguration\da935def-f80f-453a-b1ed-951b4dcdff48\TemplatePath.jpg" />
    <Content Include="Images\CertificateConfiguration\eb3efd60-c108-4213-8bdf-c3666a3f45d8\Person1Sign.png" />
    <Content Include="Images\CertificateConfiguration\eb3efd60-c108-4213-8bdf-c3666a3f45d8\Person2Sign.png" />
    <Content Include="Images\CertificateConfiguration\eb3efd60-c108-4213-8bdf-c3666a3f45d8\Person3Sign.png" />
    <Content Include="Images\CertificateConfiguration\Person2Sign.png" />
    <Content Include="Images\CertificateConfiguration\Person3Sign.png" />
    <Content Include="Images\CertificateConfiguration\Template.jpg" />
    <Content Include="Images\CertificateConfiguration\TemplatePath1.jpg" />
    <Content Include="Images\Configuration\company_logo_MHD.png" />
    <Content Include="Images\Configuration\company_logo_UOV.png" />
    <Content Include="Images\Course\C0001DMJ.png" />
    <Content Include="Images\Course\C0001LQB.jpeg" />
    <Content Include="Images\Course\C0002AVI.jpg" />
    <Content Include="Images\Course\C0002BGT.jpg" />
    <Content Include="Images\Course\C0002NRI.png" />
    <Content Include="Images\Course\C0003OGY.jpg" />
    <Content Include="Images\Course\C0003ZJV.jpg" />
    <Content Include="Images\Course\C0007AQL.png" />
    <Content Include="Images\Course\C0008BIM.jpg" />
    <Content Include="Images\Course\C0010ZZF.jpg" />
    <Content Include="Images\Course\C0011FSY.PNG" />
    <Content Include="Images\Course\C0012IGW.PNG" />
    <Content Include="Images\Course\C0013NCT.PNG" />
    <Content Include="Images\Course\C0014CMD.PNG" />
    <Content Include="Images\Course\C0015HTF.jpg" />
    <Content Include="Images\Course\C0016RKX.jpg" />
    <Content Include="Images\Course\C0017MZJ.jpg" />
    <Content Include="Images\Course\C0017WHF.png" />
    <Content Include="Images\ExternalCourse\C0001KKJ.jpg" />
    <Content Include="Images\ExternalCourse\C0002IHQ.jpg" />
    <Content Include="Images\ExternalCourse\C0003FTL.png" />
    <Content Include="Images\Trainee\T00003_ZNR.jpg" />
    <Content Include="Images\Trainee\T00004_WWV.JPEG" />
    <Content Include="Images\Trainee\T0002_XRY.jpg" />
    <Content Include="Images\Trainee\Trainee-01_NUE.png" />
    <Content Include="Images\Users\1689b10d-85b2-4448-b4ee-42e16eefd59c_AVF.jpg" />
    <Content Include="Images\Users\1689b10d-85b2-4448-b4ee-42e16eefd59c_OGL.jpg" />
    <Content Include="Images\Users\1689b10d-85b2-4448-b4ee-42e16eefd59c_YOJ.jpg" />
    <Content Include="Images\Users\8091ebd0-5c9c-4320-bfb5-0ee8cb91e974_OZX.jpg" /> -->
    <Content Include="Scripts\bootstrap.bundle.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.esm.js" />
    <Content Include="Scripts\bootstrap.esm.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <None Include="Scripts\jquery-3.6.0.intellisense.js" />
    <Content Include="Scripts\jquery-3.6.0.js" />
    <Content Include="Scripts\jquery-3.6.0.min.js" />
    <Content Include="Scripts\jquery-3.6.0.slim.js" />
    <Content Include="Scripts\jquery-3.6.0.slim.min.js" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Web.config" />
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Areas\HelpPage\Views\_ViewStart.cshtml" />
    <Content Include="Content\Site.css" />
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Scripts\bootstrap.min.js.map" />
    <Content Include="Scripts\bootstrap.js.map" />
    <Content Include="Scripts\bootstrap.esm.min.js.map" />
    <Content Include="Scripts\bootstrap.esm.js.map" />
    <Content Include="Scripts\bootstrap.bundle.min.js.map" />
    <Content Include="Scripts\bootstrap.bundle.js.map" />
    <Content Include="Scripts\jquery-3.6.0.slim.min.map" />
    <Content Include="Scripts\jquery-3.6.0.min.map" />
    <Content Include="Views\ErrorPage\Oops.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Properties\PublishProfiles\" />
    <Folder Include="Views\Device\" />
    <Folder Include="Views\GhooriCertificate\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Brac.LMS.App.Services\Brac.LMS.App.Services.csproj">
      <Project>{9087AE93-1D9D-4AEE-BFDC-5FD1B994A404}</Project>
      <Name>Brac.LMS.App.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\Brac.LMS.App.ViewModels\Brac.LMS.App.ViewModels.csproj">
      <Project>{C913671A-1513-4D41-A3DB-DA7BC9C3292F}</Project>
      <Name>Brac.LMS.App.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\Brac.LMS.Common\Brac.LMS.Common.csproj">
      <Project>{54ADBAFF-7A77-47FB-B7D3-7E335616A868}</Project>
      <Name>Brac.LMS.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Brac.LMS.DB\Brac.LMS.DB.csproj">
      <Project>{6CCC73CD-157B-441E-94E1-645E2391887B}</Project>
      <Name>Brac.LMS.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\Brac.LMS.Models\Brac.LMS.Models.csproj">
      <Project>{EC959511-D275-40BB-8E2C-DC414F4C31D5}</Project>
      <Name>Brac.LMS.Models</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>54887</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44369/</IISUrl>
          <OverrideIISAppRootUrl>True</OverrideIISAppRootUrl>
          <IISAppRootUrl>https://localhost:44369/</IISAppRootUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets'))" />
    <Error Condition="!Exists('..\packages\System.Text.Json.6.0.0\build\System.Text.Json.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\System.Text.Json.6.0.0\build\System.Text.Json.targets'))" />
  </Target>
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <Import Project="..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" />
  <Import Project="..\packages\System.Text.Json.6.0.0\build\System.Text.Json.targets" Condition="Exists('..\packages\System.Text.Json.6.0.0\build\System.Text.Json.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>